#!/usr/bin/env python3
"""
测试模型重构的完整性和正确性
"""

def test_model_structure():
    """测试模型结构"""
    try:
        # 测试所有模型都能正确导入
        from app.models import (
            # 基础模型
            Message, Token, TokenPayload, NewPassword,
            # 用户模型
            User, UserCreate, UserUpdate, UserPublic, UsersPublic,
            # 项目模型
            Item, ItemCreate, ItemUpdate, ItemPublic, ItemsPublic,
            # 文档模型
            Document, DocumentCreate, DocumentUpdate, DocumentPublic, DocumentsPublic,
            DocumentChunk, DocumentChunkCreate, DocumentChunkUpdate, DocumentChunkPublic,
            # 主题模型
            Topic, TopicCreate, TopicUpdate, TopicPublic, TopicsPublic,
            KnowledgePoint, KnowledgePointCreate, KnowledgePointUpdate, KnowledgePointPublic,
            # 对话模型
            Conversation, ConversationCreate, ConversationUpdate, ConversationPublic,
            ConversationMessage, ConversationMessageCreate, ConversationMessageUpdate,
            MessageRole, ConversationStatus
        )
        print("✅ 所有模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_model_relationships():
    """测试模型关系"""
    try:
        from app.models import User, Document, Topic, Conversation
        
        # 检查模型是否有正确的表名
        assert hasattr(Document, '__tablename__'), "Document 应该有 __tablename__"
        assert hasattr(Topic, '__tablename__'), "Topic 应该有 __tablename__"
        assert hasattr(Conversation, '__tablename__'), "Conversation 应该有 __tablename__"
        
        print("✅ 模型关系检查通过")
        return True
    except Exception as e:
        print(f"❌ 模型关系检查失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        # 测试原有的导入方式仍然有效
        from app.models import User, Item, Message
        
        # 测试模型实例化
        user_data = {
            "email": "<EMAIL>",
            "is_active": True,
            "is_superuser": False,
            "full_name": "Test User"
        }
        
        # 这应该不会抛出异常
        user_fields = User.model_fields if hasattr(User, 'model_fields') else User.__fields__
        assert 'email' in user_fields, "User 模型应该有 email 字段"
        assert 'id' in user_fields, "User 模型应该有 id 字段"
        
        print("✅ 向后兼容性检查通过")
        return True
    except Exception as e:
        print(f"❌ 向后兼容性检查失败: {e}")
        return False

def test_new_models():
    """测试新模型功能"""
    try:
        from app.models import Document, Topic, Conversation, MessageRole, ConversationStatus
        
        # 测试枚举类型
        assert MessageRole.USER == "user", "MessageRole.USER 应该等于 'user'"
        assert ConversationStatus.ACTIVE == "active", "ConversationStatus.ACTIVE 应该等于 'active'"
        
        # 测试新模型的字段
        doc_fields = Document.model_fields if hasattr(Document, 'model_fields') else Document.__fields__
        assert 'title' in doc_fields, "Document 模型应该有 title 字段"
        assert 'content' in doc_fields, "Document 模型应该有 content 字段"
        assert 'owner_id' in doc_fields, "Document 模型应该有 owner_id 字段"
        
        print("✅ 新模型功能检查通过")
        return True
    except Exception as e:
        print(f"❌ 新模型功能检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试模型重构...")
    print("=" * 50)
    
    tests = [
        test_model_structure,
        test_model_relationships,
        test_backward_compatibility,
        test_new_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 模型重构完全成功！")
        print("✅ 所有现有功能保持兼容")
        print("✅ 新模型功能正常")
        print("✅ 数据库表结构正确")
    else:
        print("❌ 部分测试失败，需要修复")
