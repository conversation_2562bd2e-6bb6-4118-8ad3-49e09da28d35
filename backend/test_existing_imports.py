#!/usr/bin/env python3
"""
测试现有代码的导入是否仍然正常工作
"""

def test_existing_route_imports():
    """测试现有路由的导入"""
    try:
        # 测试 items 路由的导入
        from app.models import Item, ItemCreate, ItemPublic, ItemsPublic, ItemUpdate, Message
        print("✅ Items 路由相关模型导入成功")
        
        # 测试 users 路由的导入
        from app.models import (
            Item,
            Message,
            UpdatePassword,
            User,
            UserCreate,
            UserPublic,
            UsersPublic,
            UserUpdate,
            UserUpdateMe,
        )
        print("✅ Users 路由相关模型导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 现有路由导入失败: {e}")
        return False

def test_crud_imports():
    """测试 CRUD 导入"""
    try:
        from app import crud
        print("✅ CRUD 模块导入成功")
        
        # 测试具体的 CRUD 函数
        if hasattr(crud, 'create_user'):
            print("✅ create_user 函数存在")
        if hasattr(crud, 'create_item'):
            print("✅ create_item 函数存在")
            
        return True
    except Exception as e:
        print(f"❌ CRUD 导入失败: {e}")
        return False

def test_api_deps_imports():
    """测试 API 依赖导入"""
    try:
        from app.api.deps import CurrentUser, SessionDep
        print("✅ API 依赖导入成功")
        
        return True
    except Exception as e:
        print(f"❌ API 依赖导入失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试现有代码兼容性...")
    
    success1 = test_existing_route_imports()
    success2 = test_crud_imports()
    success3 = test_api_deps_imports()
    
    if success1 and success2 and success3:
        print("\n🎉 所有兼容性测试通过！现有代码不受影响！")
    else:
        print("\n❌ 兼容性测试失败，需要修复问题")
