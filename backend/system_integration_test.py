#!/usr/bin/env python3
"""
系统集成测试脚本

测试整个文档处理系统的端到端功能
"""

import uuid
import time
import sys
import traceback
from typing import Dict, Any


def test_database_connection():
    """测试数据库连接"""
    print("🔗 测试数据库连接...")
    try:
        from sqlmodel import Session
        from app.core.db import engine
        
        with Session(engine) as session:
            # 简单查询测试连接
            from sqlmodel import text
            result = session.exec(text("SELECT 1")).first()
            assert result == 1
            
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_model_creation_and_validation():
    """测试模型创建和验证"""
    print("📋 测试模型创建和验证...")
    try:
        from app.models import DocumentCreate, DocumentChunkCreate, TopicCreate
        
        # 测试文档模型
        doc = DocumentCreate(
            title="系统测试文档",
            content="这是一个系统集成测试文档，用于验证整个文档处理流程。" * 10,
            file_type="txt",
            size=500
        )
        
        # 测试主题模型
        topic = TopicCreate(
            name="系统测试",
            description="系统集成测试主题"
        )
        
        print("✅ 模型创建和验证成功")
        return True
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False


def test_crud_operations():
    """测试 CRUD 操作"""
    print("💾 测试 CRUD 操作...")
    try:
        from sqlmodel import Session
        from app.core.db import engine
        from app.models import DocumentCreate, TopicCreate
        from app.crud.document import create_document, get_document, delete_document
        from app.crud.topic import create_topic, get_topic, delete_topic
        
        with Session(engine) as session:
            # 创建测试用户ID
            test_user_id = uuid.uuid4()
            
            # 测试文档 CRUD
            doc_in = DocumentCreate(
                title="CRUD测试文档",
                content="这是CRUD测试内容",
                file_type="txt",
                size=50
            )
            
            # 创建文档
            document = create_document(session=session, document_in=doc_in, owner_id=test_user_id)
            assert document.title == doc_in.title

            # 读取文档
            retrieved_doc = get_document(session=session, document_id=document.id)
            assert retrieved_doc is not None
            assert retrieved_doc.id == document.id

            # 删除文档
            deleted_doc = delete_document(session=session, document_id=document.id)
            assert deleted_doc is not None
            
            # 测试主题 CRUD
            topic_in = TopicCreate(
                name="CRUD测试主题",
                description="CRUD测试描述"
            )
            
            topic = create_topic(session=session, topic_in=topic_in)
            assert topic.name == topic_in.name

            retrieved_topic = get_topic(session=session, topic_id=topic.id)
            assert retrieved_topic is not None

            delete_topic(session=session, topic_id=topic.id)
            
        print("✅ CRUD 操作测试成功")
        return True
    except Exception as e:
        print(f"❌ CRUD 操作测试失败: {e}")
        traceback.print_exc()
        return False


def test_service_layer():
    """测试服务层"""
    print("🔧 测试服务层...")
    try:
        from sqlmodel import Session
        from app.core.db import engine
        from app.models import DocumentCreate
        from app.services.document import DocumentService, ChunkService, ProcessingService
        
        with Session(engine) as session:
            # 测试文档服务
            doc_service = DocumentService(session)
            chunk_service = ChunkService(session)
            processing_service = ProcessingService(session)
            
            # 创建测试文档
            test_user_id = uuid.uuid4()
            doc_in = DocumentCreate(
                title="服务层测试文档",
                content="这是服务层测试内容。" * 20,  # 足够长以便分割
                file_type="txt",
                size=400
            )
            
            # 创建文档（不自动处理）
            document = doc_service.create_document(doc_in, test_user_id, auto_process=False)
            assert document is not None
            
            # 测试文档分割
            chunks = chunk_service.split_document(document)
            assert len(chunks) > 0
            
            # 测试统计信息
            stats = chunk_service.get_chunk_statistics(document.id)
            assert stats["total_chunks"] > 0
            
            # 测试处理状态
            status = processing_service.get_processing_status(str(document.id))
            assert status["document_id"] == str(document.id)
            
            # 清理
            doc_service.delete_document(document.id)
            
        print("✅ 服务层测试成功")
        return True
    except Exception as e:
        print(f"❌ 服务层测试失败: {e}")
        traceback.print_exc()
        return False


def test_text_splitter_integration():
    """测试文本分割引擎集成"""
    print("✂️ 测试文本分割引擎集成...")
    try:
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.models import Document as EngineDocument
        from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
        
        # 创建引擎实例
        engine = TextSplitterEngine()
        
        # 创建测试文档
        test_content = "这是一个测试文档。" * 50  # 重复内容以便分割
        test_doc = EngineDocument(
            title="引擎测试文档",
            content=test_content,
            file_type="txt",
            size=len(test_content.encode('utf-8'))
        )
        
        # 测试 Token 策略
        token_strategy = TokenBasedStrategy(max_tokens=50)
        token_result = engine.split_document(test_doc, token_strategy)
        assert token_result.total_chunks > 0
        
        # 测试字符策略
        char_strategy = CharacterBasedStrategy(max_chars=100)
        char_result = engine.split_document(test_doc, char_strategy)
        assert char_result.total_chunks > 0
        
        print(f"✅ 文本分割引擎集成成功 (Token: {token_result.total_chunks} 块, Char: {char_result.total_chunks} 块)")
        return True
    except Exception as e:
        print(f"❌ 文本分割引擎集成失败: {e}")
        traceback.print_exc()
        return False


def test_task_system():
    """测试任务系统"""
    print("⚡ 测试任务系统...")
    try:
        from sqlmodel import Session
        from app.core.db import engine
        from app.models import DocumentCreate
        from app.services.document import DocumentService
        from app.tasks import process_document_task, reprocess_document_task
        
        with Session(engine) as session:
            # 创建测试文档
            doc_service = DocumentService(session)
            test_user_id = uuid.uuid4()
            
            doc_in = DocumentCreate(
                title="任务系统测试文档",
                content="这是任务系统测试内容。" * 15,
                file_type="txt",
                size=300
            )
            
            document = doc_service.create_document(doc_in, test_user_id, auto_process=False)
            
            # 测试处理任务（直接调用，不通过 Dramatiq）
            result = process_document_task(str(document.id))
            assert result["success"] is True
            assert "chunks_created" in result
            
            # 测试重新处理任务
            reprocess_result = reprocess_document_task(str(document.id))
            assert reprocess_result["success"] is True
            
            # 清理
            doc_service.delete_document(document.id)
            
        print("✅ 任务系统测试成功")
        return True
    except Exception as e:
        print(f"❌ 任务系统测试失败: {e}")
        traceback.print_exc()
        return False


def test_api_dependencies():
    """测试 API 依赖注入"""
    print("🔌 测试 API 依赖注入...")
    try:
        from app.api.deps import (
            DocumentServiceDep, ChunkServiceDep, ProcessingServiceDep,
            get_document_service, get_chunk_service, get_processing_service
        )
        
        print("✅ API 依赖注入测试成功")
        return True
    except Exception as e:
        print(f"❌ API 依赖注入测试失败: {e}")
        return False


def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("🔄 测试端到端工作流程...")
    try:
        from sqlmodel import Session
        from app.core.db import engine
        from app.models import DocumentCreate
        from app.services.document import DocumentService, ChunkService
        
        with Session(engine) as session:
            # 完整的文档处理工作流程
            doc_service = DocumentService(session)
            chunk_service = ChunkService(session)
            
            test_user_id = uuid.uuid4()
            
            # 1. 创建文档
            doc_in = DocumentCreate(
                title="端到端测试文档",
                content="这是一个完整的端到端测试文档，用于验证整个文档处理流程的正确性。" * 10,
                file_type="txt",
                size=600
            )
            
            document = doc_service.create_document(doc_in, test_user_id, auto_process=False)
            print(f"  📄 文档已创建: {document.title}")
            
            # 2. 分割文档
            chunks = chunk_service.split_document(document)
            print(f"  ✂️ 文档已分割: {len(chunks)} 个块")
            
            # 3. 获取统计信息
            stats = chunk_service.get_chunk_statistics(document.id)
            print(f"  📊 统计信息: {stats['total_chunks']} 块, {stats['total_characters']} 字符")
            
            # 4. 验证分块内容
            retrieved_chunks = chunk_service.get_document_chunks(document.id)
            assert len(retrieved_chunks) == len(chunks)
            
            # 5. 清理
            doc_service.delete_document(document.id)
            print("  🗑️ 测试数据已清理")
            
        print("✅ 端到端工作流程测试成功")
        return True
    except Exception as e:
        print(f"❌ 端到端工作流程测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("="*80)
    print("🚀 系统集成测试开始")
    print("="*80)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("模型创建和验证", test_model_creation_and_validation),
        ("CRUD 操作", test_crud_operations),
        ("服务层", test_service_layer),
        ("文本分割引擎集成", test_text_splitter_integration),
        ("任务系统", test_task_system),
        ("API 依赖注入", test_api_dependencies),
        ("端到端工作流程", test_end_to_end_workflow),
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'-'*60}")
        test_start = time.time()
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 执行时发生异常: {e}")
            results[test_name] = False
        
        test_duration = time.time() - test_start
        print(f"⏱️ 耗时: {test_duration:.2f}s")
    
    # 输出总结
    total_duration = time.time() - start_time
    print("\n" + "="*80)
    print("📊 系统集成测试结果总结")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:25} : {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总计: {passed}/{total} 测试通过")
    print(f"⏱️ 总耗时: {total_duration:.2f}s")
    
    if passed == total:
        print("\n🎉 所有系统集成测试通过！系统运行正常。")
        return True
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查具体错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
