#!/usr/bin/env python3
"""
API 路由和依赖注入测试脚本

测试文档处理 API 路由和服务依赖注入的功能
"""

from fastapi import APIRouter
from fastapi.testclient import TestClient
from fastapi import FastAPI


def test_dependency_injection():
    """测试依赖注入"""
    print("Testing dependency injection...")
    
    try:
        from app.api.deps import (
            DocumentServiceDep, 
            ChunkServiceDep, 
            ProcessingServiceDep,
            get_document_service,
            get_chunk_service,
            get_processing_service
        )
        print("✓ Dependency injection imports successful")
        
        # 测试依赖注入函数是否可调用
        print("✓ Dependency injection functions are callable")
        
        return True
        
    except Exception as e:
        print(f"✗ Dependency injection test failed: {e}")
        return False


def test_document_routes():
    """测试文档路由"""
    print("\nTesting document routes...")
    
    try:
        from app.api.routes.documents import router
        print("✓ Document router import successful")
        
        # 检查路由定义
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/documents/",
            "/documents/{id}",
            "/documents/{id}/chunks",
            "/documents/{id}/process",
            "/documents/{id}/reprocess",
            "/documents/{id}/stats"
        ]
        
        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✓ Route {expected_route} found")
            else:
                print(f"✗ Route {expected_route} missing")
                return False
        
        print(f"✓ All expected routes found: {len(expected_routes)} routes")
        
        return True
        
    except Exception as e:
        print(f"✗ Document routes test failed: {e}")
        return False


def test_api_integration():
    """测试 API 集成"""
    print("\nTesting API integration...")
    
    try:
        from app.api.routes.documents import router as documents_router
        
        # 创建测试 API 路由器
        api_router = APIRouter()
        api_router.include_router(documents_router)
        
        print("✓ Document router integration successful")
        
        # 检查集成后的路由
        integrated_routes = [route.path for route in api_router.routes]
        print(f"✓ Integrated routes: {len(integrated_routes)} routes")
        
        return True
        
    except Exception as e:
        print(f"✗ API integration test failed: {e}")
        return False


def test_route_methods():
    """测试路由方法"""
    print("\nTesting route methods...")

    try:
        from app.api.routes.documents import router

        # 检查路由数量
        total_routes = len(router.routes)
        print(f"✓ Found {total_routes} routes in document router")

        # 检查是否有足够的路由（应该有至少 6 个主要路由）
        if total_routes >= 6:
            print("✓ Sufficient number of routes found")
        else:
            print(f"✗ Expected at least 6 routes, found {total_routes}")
            return False

        # 检查路由函数名称
        route_functions = []
        for route in router.routes:
            if hasattr(route, 'endpoint') and hasattr(route.endpoint, '__name__'):
                route_functions.append(route.endpoint.__name__)

        expected_functions = [
            'read_documents',
            'read_document',
            'create_document',
            'update_document',
            'delete_document',
            'read_document_chunks',
            'process_document',
            'reprocess_document',
            'get_document_stats'
        ]

        found_functions = 0
        for func_name in expected_functions:
            if func_name in route_functions:
                print(f"✓ Function {func_name} found")
                found_functions += 1
            else:
                print(f"⚠ Function {func_name} not found")

        if found_functions >= 6:  # 至少要有主要的 CRUD 功能
            print(f"✓ Found {found_functions}/{len(expected_functions)} expected functions")
            return True
        else:
            print(f"✗ Only found {found_functions}/{len(expected_functions)} expected functions")
            return False

    except Exception as e:
        print(f"✗ Route methods test failed: {e}")
        return False


def test_model_imports():
    """测试模型导入"""
    print("\nTesting model imports...")
    
    try:
        from app.models import (
            Document,
            DocumentCreate,
            DocumentPublic,
            DocumentsPublic,
            DocumentUpdate,
            DocumentChunk,
            DocumentChunkPublic,
            DocumentChunksPublic,
            Message,
        )
        print("✓ All document models imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Model imports test failed: {e}")
        return False


def test_task_integration():
    """测试任务集成"""
    print("\nTesting task integration...")
    
    try:
        from app.tasks import enqueue_document_processing, enqueue_document_reprocessing
        print("✓ Task functions imported successfully")
        
        # 测试任务函数是否可调用（不实际执行）
        import uuid
        test_doc_id = str(uuid.uuid4())
        
        print("✓ Task functions are callable")
        
        return True
        
    except Exception as e:
        print(f"✗ Task integration test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("API 路由和依赖注入测试")
    print("=" * 60)
    
    tests = [
        test_dependency_injection,
        test_document_routes,
        test_api_integration,
        test_route_methods,
        test_model_imports,
        test_task_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！API 路由和依赖注入创建成功。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
