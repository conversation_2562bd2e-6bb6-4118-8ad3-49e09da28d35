#!/usr/bin/env python3
"""
简化系统测试脚本

专注测试核心功能，避免数据库依赖问题
"""

import sys
import time


def test_core_imports():
    """测试核心模块导入"""
    print("📦 测试核心模块导入...")
    try:
        # 测试模型导入
        from app.models import (
            Document, DocumentCreate, DocumentChunk, DocumentChunkCreate,
            Topic, TopicCreate, Conversation, ConversationCreate
        )
        
        # 测试服务导入
        from app.services.document import DocumentService, ChunkService, ProcessingService
        
        # 测试 API 导入
        from app.api.routes.documents import router
        from app.api.deps import DocumentServiceDep, ChunkServiceDep
        
        # 测试任务导入
        from app.tasks import process_document_task, reprocess_document_task
        
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False


def test_text_splitter_engine():
    """测试文本分割引擎"""
    print("✂️ 测试文本分割引擎...")
    try:
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.models import Document as EngineDocument
        from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
        
        # 创建引擎
        engine = TextSplitterEngine()
        
        # 创建测试文档
        content = "这是一个测试文档。" * 30
        test_doc = EngineDocument(
            title="测试文档",
            content=content,
            file_type="txt",
            size=len(content.encode('utf-8'))
        )
        
        # 测试 Token 策略
        token_strategy = TokenBasedStrategy(max_tokens=50)
        token_result = engine.split_document(test_doc, token_strategy)
        
        # 测试字符策略
        char_strategy = CharacterBasedStrategy(max_chars=100)
        char_result = engine.split_document(test_doc, char_strategy)
        
        print(f"✅ 文本分割引擎测试成功 (Token: {token_result.total_chunks} 块, Char: {char_result.total_chunks} 块)")
        return True
    except Exception as e:
        print(f"❌ 文本分割引擎测试失败: {e}")
        return False


def test_model_creation():
    """测试模型创建"""
    print("📋 测试模型创建...")
    try:
        import uuid
        from app.models import DocumentCreate, DocumentChunkCreate, TopicCreate
        
        # 测试文档模型创建
        doc_create = DocumentCreate(
            title="测试文档",
            content="这是测试内容",
            file_type="txt",
            size=20
        )
        
        # 测试文档块模型创建
        chunk_create = DocumentChunkCreate(
            document_id=uuid.uuid4(),
            content="测试块内容",
            chunk_index=0,
            start_char=0,
            end_char=10,
            token_count=3
        )
        
        # 测试主题模型创建
        topic_create = TopicCreate(
            name="测试主题",
            description="测试描述"
        )
        
        print("✅ 模型创建测试成功")
        return True
    except Exception as e:
        print(f"❌ 模型创建测试失败: {e}")
        return False


def test_api_router():
    """测试 API 路由器"""
    print("🌐 测试 API 路由器...")
    try:
        from fastapi import APIRouter
        from app.api.routes.documents import router as documents_router
        
        # 创建主路由器
        main_router = APIRouter()
        main_router.include_router(documents_router)
        
        # 检查路由
        routes = list(main_router.routes)
        route_paths = [route.path for route in routes]
        
        expected_paths = ["/", "/{id}", "/{id}/chunks", "/{id}/process", "/{id}/reprocess", "/{id}/stats"]
        found_paths = 0
        
        for expected in expected_paths:
            if any(expected in path for path in route_paths):
                found_paths += 1
        
        print(f"✅ API 路由器测试成功 (找到 {found_paths}/{len(expected_paths)} 个预期路由)")
        return True
    except Exception as e:
        print(f"❌ API 路由器测试失败: {e}")
        return False


def test_task_functions():
    """测试任务函数定义"""
    print("⚡ 测试任务函数...")
    try:
        from app.tasks import (
            process_document_task,
            reprocess_document_task,
            process_document_with_strategy_task,
            enqueue_document_processing,
            enqueue_document_reprocessing
        )
        
        # 检查函数是否可调用
        assert callable(process_document_task)
        assert callable(reprocess_document_task)
        assert callable(process_document_with_strategy_task)
        assert callable(enqueue_document_processing)
        assert callable(enqueue_document_reprocessing)
        
        print("✅ 任务函数测试成功")
        return True
    except Exception as e:
        print(f"❌ 任务函数测试失败: {e}")
        return False


def test_service_instantiation():
    """测试服务实例化（无数据库）"""
    print("🔧 测试服务实例化...")
    try:
        from app.services.document import ProcessingService
        
        # 创建处理服务（不传入 session）
        service = ProcessingService()
        
        # 检查服务属性
        assert hasattr(service, 'document_service')
        assert hasattr(service, 'chunk_service')
        
        print("✅ 服务实例化测试成功")
        return True
    except Exception as e:
        print(f"❌ 服务实例化测试失败: {e}")
        return False


def test_dependency_injection():
    """测试依赖注入定义"""
    print("🔌 测试依赖注入...")
    try:
        from app.api.deps import (
            DocumentServiceDep,
            ChunkServiceDep,
            ProcessingServiceDep,
            get_document_service,
            get_chunk_service,
            get_processing_service
        )
        
        # 检查类型注解是否正确定义
        assert DocumentServiceDep is not None
        assert ChunkServiceDep is not None
        assert ProcessingServiceDep is not None
        
        # 检查函数是否可调用
        assert callable(get_document_service)
        assert callable(get_chunk_service)
        assert callable(get_processing_service)
        
        print("✅ 依赖注入测试成功")
        return True
    except Exception as e:
        print(f"❌ 依赖注入测试失败: {e}")
        return False


def test_integration_workflow():
    """测试集成工作流程（模拟）"""
    print("🔄 测试集成工作流程...")
    try:
        from app.models import DocumentCreate
        from app.services.document import ProcessingService
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.models import Document as EngineDocument
        from engines.text_splitter.strategies import TokenBasedStrategy
        
        # 1. 创建文档模型
        content = "这是一个集成测试文档，用于验证整个工作流程。" * 20
        doc_create = DocumentCreate(
            title="集成测试文档",
            content=content,
            file_type="txt",
            size=len(content.encode('utf-8'))
        )
        
        # 2. 模拟文档转换
        engine_doc = EngineDocument(
            title=doc_create.title,
            content=doc_create.content,
            file_type=doc_create.file_type,
            size=doc_create.size
        )
        
        # 3. 执行文本分割
        engine = TextSplitterEngine()
        strategy = TokenBasedStrategy(max_tokens=100)
        result = engine.split_document(engine_doc, strategy)
        
        # 4. 验证结果
        assert result.total_chunks > 0
        assert len(result.chunks) == result.total_chunks
        
        print(f"✅ 集成工作流程测试成功 (生成 {result.total_chunks} 个文档块)")
        return True
    except Exception as e:
        print(f"❌ 集成工作流程测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("="*80)
    print("🚀 简化系统测试")
    print("="*80)
    
    tests = [
        ("核心模块导入", test_core_imports),
        ("文本分割引擎", test_text_splitter_engine),
        ("模型创建", test_model_creation),
        ("API 路由器", test_api_router),
        ("任务函数", test_task_functions),
        ("服务实例化", test_service_instantiation),
        ("依赖注入", test_dependency_injection),
        ("集成工作流程", test_integration_workflow),
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'-'*60}")
        test_start = time.time()
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 执行时发生异常: {e}")
            results[test_name] = False
        
        test_duration = time.time() - test_start
        print(f"⏱️ 耗时: {test_duration:.2f}s")
    
    # 输出总结
    total_duration = time.time() - start_time
    print("\n" + "="*80)
    print("📊 简化系统测试结果总结")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总计: {passed}/{total} 测试通过")
    print(f"⏱️ 总耗时: {total_duration:.2f}s")
    
    if passed == total:
        print("\n🎉 所有简化系统测试通过！核心功能运行正常。")
        return True
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查具体错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
