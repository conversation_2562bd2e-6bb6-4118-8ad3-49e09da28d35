"""
文档相关数据模型
包含文档、文档块等相关模型
"""
import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from .user import User


# Shared properties
class DocumentBase(SQLModel):
    title: str = Field(max_length=255)
    content: str
    file_type: str = Field(max_length=50)
    size: int


# Properties to receive via API on creation
class DocumentCreate(DocumentBase):
    pass


# Properties to receive via API on update
class DocumentUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    content: str | None = None
    file_type: str | None = Field(default=None, max_length=50)
    size: int | None = None


# Database model, database table inferred from class name
class Document(DocumentBase, table=True):
    __tablename__ = "document"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    owner: "User" = Relationship()
    chunks: list["DocumentChunk"] = Relationship(back_populates="document", cascade_delete=True)


# Properties to return via API, id is always required
class DocumentPublic(DocumentBase):
    id: uuid.UUID
    owner_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class DocumentsPublic(SQLModel):
    data: list[DocumentPublic]
    count: int


# Document chunk models
class DocumentChunkBase(SQLModel):
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    token_count: Optional[int] = None


class DocumentChunkCreate(DocumentChunkBase):
    document_id: uuid.UUID


class DocumentChunkUpdate(SQLModel):
    content: str | None = None
    chunk_index: int | None = None
    start_char: int | None = None
    end_char: int | None = None
    token_count: int | None = None


class DocumentChunk(DocumentChunkBase, table=True):
    __tablename__ = "document_chunk"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    document_id: uuid.UUID = Field(foreign_key="document.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    document: Document | None = Relationship(back_populates="chunks")


class DocumentChunkPublic(DocumentChunkBase):
    id: uuid.UUID
    document_id: uuid.UUID
    created_at: datetime


class DocumentChunksPublic(SQLModel):
    data: list[DocumentChunkPublic]
    count: int
