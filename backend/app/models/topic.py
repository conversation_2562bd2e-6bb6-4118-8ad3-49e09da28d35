"""
主题相关数据模型
包含学习主题、知识点等相关模型
"""
import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from .user import User
    from .document import Document


# Shared properties
class TopicBase(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    category: str | None = Field(default=None, max_length=100)
    difficulty_level: int = Field(default=1, ge=1, le=5)  # 1-5 difficulty scale


# Properties to receive via API on creation
class TopicCreate(TopicBase):
    pass


# Properties to receive via API on update
class TopicUpdate(SQLModel):
    name: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    category: str | None = Field(default=None, max_length=100)
    difficulty_level: int | None = Field(default=None, ge=1, le=5)


# Database model, database table inferred from class name
class Topic(TopicBase, table=True):
    __tablename__ = "topic"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    owner: "User" = Relationship()
    knowledge_points: list["KnowledgePoint"] = Relationship(back_populates="topic", cascade_delete=True)


# Properties to return via API, id is always required
class TopicPublic(TopicBase):
    id: uuid.UUID
    owner_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class TopicsPublic(SQLModel):
    data: list[TopicPublic]
    count: int


# Knowledge point models
class KnowledgePointBase(SQLModel):
    title: str = Field(max_length=255)
    content: str
    importance: int = Field(default=3, ge=1, le=5)  # 1-5 importance scale
    mastery_level: int = Field(default=0, ge=0, le=5)  # 0-5 mastery level


class KnowledgePointCreate(KnowledgePointBase):
    topic_id: uuid.UUID


class KnowledgePointUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    content: str | None = None
    importance: int | None = Field(default=None, ge=1, le=5)
    mastery_level: int | None = Field(default=None, ge=0, le=5)


class KnowledgePoint(KnowledgePointBase, table=True):
    __tablename__ = "knowledge_point"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    topic_id: uuid.UUID = Field(foreign_key="topic.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationships
    topic: Topic | None = Relationship(back_populates="knowledge_points")


class KnowledgePointPublic(KnowledgePointBase):
    id: uuid.UUID
    topic_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class KnowledgePointsPublic(SQLModel):
    data: list[KnowledgePointPublic]
    count: int
