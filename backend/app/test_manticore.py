"""
Test script for Manticore Search integration.

This script tests the Manticore client functionality.
"""

import asyncio
import logging
import sys
from typing import Dict, Any

from manticore_client import ManticoreClient, init_manticore_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_health_check(client: ManticoreClient) -> bool:
    """Test Manticore health check."""
    logger.info("Testing health check...")
    try:
        is_healthy = await client.health_check()
        if is_healthy:
            logger.info("✅ Health check passed")
            return True
        else:
            logger.error("❌ Health check failed")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return False


async def test_create_table(client: ManticoreClient) -> bool:
    """Test creating a table."""
    logger.info("Testing table creation...")
    try:
        # Define a simple test table schema
        schema = {
            "title": {"type": "text"},
            "content": {"type": "text"},
            "category_id": {"type": "int"},
            "created_at": {"type": "timestamp"}
        }
        
        # Try to create the table
        success = await client.create_table("test_table", schema)
        if success:
            logger.info("✅ Table creation passed")
            return True
        else:
            logger.error("❌ Table creation failed")
            return False
    except Exception as e:
        logger.error(f"❌ Table creation error: {e}")
        return False


async def test_insert_document(client: ManticoreClient) -> bool:
    """Test inserting a document."""
    logger.info("Testing document insertion...")
    try:
        # Test document
        document = {
            "id": 1,
            "title": "Test Document",
            "content": "This is a test document for Manticore Search integration.",
            "category_id": 1,
            "created_at": 1640995200  # 2022-01-01 00:00:00 UTC
        }
        
        success = await client.insert_document("test_table", document)
        if success:
            logger.info("✅ Document insertion passed")
            return True
        else:
            logger.error("❌ Document insertion failed")
            return False
    except Exception as e:
        logger.error(f"❌ Document insertion error: {e}")
        return False


async def test_search(client: ManticoreClient) -> bool:
    """Test searching documents."""
    logger.info("Testing document search...")
    try:
        # Search for the test document
        results = await client.search(
            table_name="test_table",
            query="test document",
            limit=10
        )
        
        if "error" not in results and results["total"] > 0:
            logger.info(f"✅ Search passed - found {results['total']} results")
            logger.info(f"First result: {results['hits'][0] if results['hits'] else 'None'}")
            return True
        else:
            logger.error(f"❌ Search failed - {results}")
            return False
    except Exception as e:
        logger.error(f"❌ Search error: {e}")
        return False


async def test_sql_execution(client: ManticoreClient) -> bool:
    """Test raw SQL execution."""
    logger.info("Testing SQL execution...")
    try:
        # Test SHOW TABLES
        result = await client.execute_sql("SHOW TABLES")
        if "error" not in result:
            logger.info("✅ SQL execution passed")
            logger.info(f"Tables: {result}")
            return True
        else:
            logger.error(f"❌ SQL execution failed: {result}")
            return False
    except Exception as e:
        logger.error(f"❌ SQL execution error: {e}")
        return False


async def cleanup_test_data(client: ManticoreClient):
    """Clean up test data."""
    logger.info("Cleaning up test data...")
    try:
        await client.execute_sql("DROP TABLE IF EXISTS test_table")
        logger.info("✅ Cleanup completed")
    except Exception as e:
        logger.warning(f"⚠️ Cleanup warning: {e}")


async def run_all_tests():
    """Run all Manticore tests."""
    logger.info("Starting Manticore integration tests...")
    
    # Initialize client
    try:
        client = await init_manticore_client(
            host="localhost",  # Use localhost for testing
            port=9308,
            scheme="http"
        )
    except Exception as e:
        logger.error(f"❌ Failed to initialize client: {e}")
        return False
    
    # Track test results
    test_results = []
    
    # Run tests using async context manager
    async with client:
        test_results.append(await test_health_check(client))
        test_results.append(await test_sql_execution(client))
        test_results.append(await test_create_table(client))
        test_results.append(await test_insert_document(client))
        test_results.append(await test_search(client))

        # Cleanup
        await cleanup_test_data(client)
    
    # Summary
    passed = sum(test_results)
    total = len(test_results)
    
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed")
        return False


async def test_connection_only():
    """Test only the connection to Manticore."""
    logger.info("Testing Manticore connection...")

    try:
        client = ManticoreClient(
            host="localhost",
            port=9308,
            scheme="http"
        )

        async with client:
            is_healthy = await client.health_check()

        if is_healthy:
            logger.info("✅ Connection test passed")
            return True
        else:
            logger.error("❌ Connection test failed")
            return False

    except Exception as e:
        logger.error(f"❌ Connection test error: {e}")
        return False


if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--connection-only":
        # Test connection only
        success = asyncio.run(test_connection_only())
    else:
        # Run all tests
        success = asyncio.run(run_all_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
