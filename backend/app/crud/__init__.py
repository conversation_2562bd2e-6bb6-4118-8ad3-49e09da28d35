"""
CRUD 操作模块
重新导出所有 CRUD 函数以保持向后兼容性
"""

# 用户相关 CRUD 操作
from .user import (
    authenticate,
    create_user,
    get_user_by_email,
    update_user,
    user,  # CRUD 实例
)

# 项目相关 CRUD 操作
from .item import (
    create_item,
    delete_item,
    get_item,
    get_items_by_owner,
    item,  # CRUD 实例
    update_item,
)

# 文档相关 CRUD 操作
from .document import (
    create_document,
    create_document_chunk,
    delete_document,
    delete_document_chunks,
    document,  # CRUD 实例
    document_chunk,  # CRUD 实例
    get_document,
    get_document_chunks,
    get_documents_by_owner,
    update_document,
)

# 主题相关 CRUD 操作
from .topic import (
    create_knowledge_point,
    create_topic,
    delete_topic,
    get_knowledge_points_by_topic,
    get_topic,
    get_topics_by_owner,
    knowledge_point,  # CRUD 实例
    topic,  # CRUD 实例
    update_knowledge_point_mastery,
    update_topic,
)

# 对话相关 CRUD 操作
from .conversation import (
    conversation,  # CRUD 实例
    conversation_message,  # CRUD 实例
    create_conversation,
    create_conversation_message,
    get_conversation,
    get_conversation_messages,
    get_conversations_by_owner,
    update_conversation,
    update_conversation_status,
)

# 基础 CRUD 类
from .base import CRUDBase, count_by_owner, get_by_owner

# 为了保持完全的向后兼容性，确保所有原有的导入都能正常工作
__all__ = [
    # 用户相关
    "create_user",
    "update_user",
    "get_user_by_email",
    "authenticate",
    "user",
    # 项目相关
    "create_item",
    "get_item",
    "get_items_by_owner",
    "update_item",
    "delete_item",
    "item",
    # 文档相关
    "create_document",
    "get_document",
    "get_documents_by_owner",
    "update_document",
    "delete_document",
    "create_document_chunk",
    "get_document_chunks",
    "delete_document_chunks",
    "document",
    "document_chunk",
    # 主题相关
    "create_topic",
    "get_topic",
    "get_topics_by_owner",
    "update_topic",
    "delete_topic",
    "create_knowledge_point",
    "get_knowledge_points_by_topic",
    "update_knowledge_point_mastery",
    "topic",
    "knowledge_point",
    # 对话相关
    "create_conversation",
    "get_conversation",
    "get_conversations_by_owner",
    "update_conversation",
    "update_conversation_status",
    "create_conversation_message",
    "get_conversation_messages",
    "conversation",
    "conversation_message",
    # 基础类和工具
    "CRUDBase",
    "get_by_owner",
    "count_by_owner",
]
