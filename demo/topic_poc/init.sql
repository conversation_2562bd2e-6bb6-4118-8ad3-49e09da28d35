-- Topic POC 数据库初始化脚本

-- 创建用户表（模拟）
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试用户
INSERT INTO users (id, username, email) VALUES 
(1, 'test_user', '<EMAIL>')
ON CONFLICT (id) DO NOTHING;

-- 创建主题表
CREATE TABLE IF NOT EXISTS topics (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    user_id INTEGER NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建主题文档关联表
CREATE TABLE IF NOT EXISTS topic_document_links (
    id SERIAL PRIMARY KEY,
    topic_id INTEGER NOT NULL REFERENCES topics(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(topic_id, document_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_topics_user_id ON topics(user_id);
CREATE INDEX IF NOT EXISTS idx_topics_active ON topics(is_active);
CREATE INDEX IF NOT EXISTS idx_topic_docs_topic_id ON topic_document_links(topic_id);
CREATE INDEX IF NOT EXISTS idx_topic_docs_document_id ON topic_document_links(document_id);

-- 插入示例数据
INSERT INTO topics (title, description, user_id) VALUES 
('机器学习基础', '学习机器学习的基本概念和算法', 1),
('深度学习进阶', '深入理解神经网络和深度学习技术', 1),
('自然语言处理', 'NLP技术和应用实践', 1)
ON CONFLICT DO NOTHING;
