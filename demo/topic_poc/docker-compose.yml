version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:13
    container_name: topic_poc_postgres
    environment:
      POSTGRES_DB: topic_poc
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d topic_poc"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:6-alpine
    container_name: topic_poc_redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Manticore Search
  manticore:
    image: manticoresearch/manticore:6.0.4
    container_name: topic_poc_manticore
    ports:
      - "9306:9306"  # MySQL protocol
      - "9308:9308"  # HTTP API
      - "9312:9312"  # SphinxAPI
    volumes:
      - manticore_data:/var/lib/manticore
      - ./manticore.conf:/etc/manticoresearch/manticore.conf
    environment:
      - EXTRA=1
    healthcheck:
      test: ["CMD-SHELL", "mysql -h127.0.0.1 -P9306 -e 'SHOW TABLES'"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Topic Service POC
  topic_service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: topic_poc_service
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/topic_poc
      - REDIS_URL=redis://redis:6379/0
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
      - MANTICORE_HTTP_PORT=9308
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      manticore:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:
  redis_data:
  manticore_data:
