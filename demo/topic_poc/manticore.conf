# Manticore Search configuration for Topic POC

searchd {
    listen = 9306:mysql41
    listen = 9308:http
    listen = 9312

    log = /var/log/manticore/searchd.log
    query_log = /var/log/manticore/query.log
    pid_file = /var/run/manticore/searchd.pid
    data_dir = /var/lib/manticore

    # 查询缓存
    qcache_max_bytes = 256M
    qcache_thresh_msec = 1000
    qcache_ttl_sec = 3600

    # 网络设置
    client_timeout = 300
    max_packet_size = 128M

    # 性能优化
    max_children = 0
    max_matches = 1000
}

# 文档片段表
table docs_chunks {
    type = rt
    path = /var/lib/manticore/docs_chunks
    
    # 字段定义
    rt_field = content
    rt_field = title
    rt_attr_uint = topic_id
    rt_attr_uint = document_id
    rt_attr_string = category
    rt_attr_timestamp = created_at
    
    # 全文搜索设置
    charset_table = 0..9, A..Z->a..z, _, a..z, U+410..U+42F->U+430..U+44F, U+430..U+44F
    min_word_len = 2
    
    # 索引设置
    morphology = stem_en, stem_ru
}
