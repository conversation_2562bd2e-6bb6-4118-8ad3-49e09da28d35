#!/usr/bin/env python3
"""
Topic Service POC 测试脚本

测试所有 API 端点和功能
"""

import asyncio
import httpx
import time
from typing import Dict, Any


class TopicPOCTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = None
        self.test_results = []
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    async def test_health_check(self):
        """测试健康检查"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                checks = data.get("checks", {})
                details = f"状态: {data.get('status')}, 检查: {checks}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("健康检查", success, details)
            return success
        except Exception as e:
            self.log_test("健康检查", False, str(e))
            return False
    
    async def test_create_topic(self) -> Dict[str, Any]:
        """测试创建主题"""
        try:
            topic_data = {
                "title": "测试主题",
                "description": "这是一个测试主题",
                "user_id": 1
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/topics",
                json=topic_data
            )
            
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"主题ID: {data.get('id')}, 标题: {data.get('title')}"
                self.log_test("创建主题", True, details)
                return data
            else:
                self.log_test("创建主题", False, f"状态码: {response.status_code}, 响应: {response.text}")
                return {}
        except Exception as e:
            self.log_test("创建主题", False, str(e))
            return {}
    
    async def test_get_topic(self, topic_id: int):
        """测试获取主题"""
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/topics/{topic_id}")
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"标题: {data.get('title')}, 文档数: {data.get('document_count')}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("获取主题", success, details)
            return success
        except Exception as e:
            self.log_test("获取主题", False, str(e))
            return False
    
    async def test_attach_document(self, topic_id: int):
        """测试关联文档"""
        try:
            doc_data = {"document_id": 123}
            response = await self.client.post(
                f"{self.base_url}/api/v1/topics/{topic_id}/documents",
                json=doc_data
            )
            
            success = response.status_code == 200
            
            if success:
                data = response.json()
                details = f"消息: {data.get('message')}"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("关联文档", success, details)
            return success
        except Exception as e:
            self.log_test("关联文档", False, str(e))
            return False
    
    async def test_topic_memory(self, topic_id: int):
        """测试主题记忆检索"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/topics/{topic_id}/memory?limit=3"
            )
            
            success = response.status_code == 200
            
            if success:
                data = response.json()
                total = data.get('total', 0)
                details = f"检索到 {total} 个记忆锚点"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("主题记忆检索", success, details)
            return success
        except Exception as e:
            self.log_test("主题记忆检索", False, str(e))
            return False
    
    async def test_list_topics(self):
        """测试主题列表"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/topics?user_id=1&limit=10"
            )
            
            success = response.status_code == 200
            
            if success:
                data = response.json()
                total = data.get('total', 0)
                details = f"用户共有 {total} 个主题"
            else:
                details = f"状态码: {response.status_code}"
            
            self.log_test("主题列表", success, details)
            return success
        except Exception as e:
            self.log_test("主题列表", False, str(e))
            return False
    
    async def test_api_documentation(self):
        """测试 API 文档"""
        try:
            response = await self.client.get(f"{self.base_url}/docs")
            success = response.status_code == 200
            details = "API 文档可访问" if success else f"状态码: {response.status_code}"
            self.log_test("API 文档", success, details)
            return success
        except Exception as e:
            self.log_test("API 文档", False, str(e))
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 Topic Service POC 测试...")
        print(f"🎯 测试目标: {self.base_url}")
        print("-" * 50)
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        await asyncio.sleep(2)
        
        # 1. 健康检查
        health_ok = await self.test_health_check()
        if not health_ok:
            print("❌ 健康检查失败，跳过其他测试")
            return
        
        # 2. API 文档
        await self.test_api_documentation()
        
        # 3. 创建主题
        topic = await self.test_create_topic()
        if not topic:
            print("❌ 创建主题失败，跳过相关测试")
            return
        
        topic_id = topic.get('id')
        
        # 4. 获取主题
        await self.test_get_topic(topic_id)
        
        # 5. 关联文档
        await self.test_attach_document(topic_id)
        
        # 6. 再次获取主题（验证文档数量）
        await self.test_get_topic(topic_id)
        
        # 7. 主题记忆检索
        await self.test_topic_memory(topic_id)
        
        # 8. 主题列表
        await self.test_list_topics()
        
        # 测试总结
        print("-" * 50)
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['details']}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！POC 验证成功！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查服务状态")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Topic Service POC 测试")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="服务 URL (默认: http://localhost:8000)"
    )
    args = parser.parse_args()
    
    async with TopicPOCTester(args.url) as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
