"""
Topic Service POC - 主题服务概念验证

基于 PROJECT_BLUEPRINT.md 的技术栈实现：
FastAPI, Pydantic 2.5+, Uvicorn, SQLite (简化版), Redis 6+, Manticore Search 6.0+
"""

import asyncio
import sqlite3
import time
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager

import httpx
import pymysql
import redis
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field


# ===== 配置 =====
class Settings(BaseModel):
    # 数据库配置 (使用 SQLite 简化演示)
    database_path: str = "./topic_poc.db"

    # Redis 配置
    redis_url: str = "redis://localhost:6379/0"

    # Manticore 配置
    manticore_host: str = "localhost"
    manticore_port: int = 9306
    manticore_http_port: int = 9308

    # 外部服务
    user_service_url: str = "http://localhost:8002"
    document_service_url: str = "http://localhost:8005"


settings = Settings()


# ===== 数据模型 =====
class TopicCreate(BaseModel):
    title: str = Field(min_length=1, max_length=200)
    description: Optional[str] = Field(default=None, max_length=1000)
    user_id: int = Field(default=1)  # 固定测试用户


class TopicResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    user_id: int
    is_active: bool
    created_at: str
    updated_at: str
    document_count: int = 0


class AttachDocument(BaseModel):
    document_id: int


class MemorySearchResult(BaseModel):
    id: int
    content: str
    relevance_score: float
    metadata: Dict[str, Any] = {}


# ===== 数据库连接 =====
db_connection = None
redis_client = None


def get_db():
    """获取数据库连接"""
    conn = sqlite3.connect(settings.database_path, check_same_thread=False)
    conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
    try:
        yield conn
    finally:
        conn.close()


def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(settings.database_path, check_same_thread=False)
    cursor = conn.cursor()

    # 创建主题表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS topics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            user_id INTEGER NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # 创建主题文档关联表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS topic_document_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            topic_id INTEGER NOT NULL,
            document_id INTEGER NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (topic_id) REFERENCES topics (id),
            UNIQUE(topic_id, document_id)
        )
    """)

    conn.commit()
    conn.close()


# ===== Manticore 客户端 =====
class ManticoreClient:
    def __init__(self):
        self.host = settings.manticore_host
        self.port = settings.manticore_port
    
    def get_connection(self):
        """获取 Manticore 连接"""
        try:
            return pymysql.connect(
                host=self.host,
                port=self.port,
                charset='utf8mb4',
                autocommit=True,
                cursorclass=pymysql.cursors.DictCursor
            )
        except Exception as e:
            print(f"Manticore 连接失败: {e}")
            return None
    
    def search_topic_memory(self, topic_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索主题相关的长期记忆锚点"""
        connection = self.get_connection()
        if not connection:
            return []
        
        try:
            with connection.cursor() as cursor:
                # 模拟搜索主题相关的文档片段
                sql = """
                SELECT id, content, WEIGHT() as relevance_score
                FROM docs_chunks 
                WHERE MATCH('topic_id:{}')
                ORDER BY relevance_score DESC
                LIMIT {}
                """.format(topic_id, limit)
                
                cursor.execute(sql)
                results = cursor.fetchall()
                return results or []
        except Exception as e:
            print(f"Manticore 搜索失败: {e}")
            return []
        finally:
            connection.close()


manticore_client = ManticoreClient()


# ===== 服务层 =====
class TopicService:
    def __init__(self, conn: sqlite3.Connection):
        self.conn = conn

    def create_topic(self, topic_data: TopicCreate) -> Dict[str, Any]:
        """创建主题"""
        cursor = self.conn.cursor()
        now = datetime.utcnow().isoformat()

        cursor.execute("""
            INSERT INTO topics (title, description, user_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """, (topic_data.title, topic_data.description, topic_data.user_id, now, now))

        topic_id = cursor.lastrowid
        self.conn.commit()

        return self.get_topic(topic_id)

    def get_topic(self, topic_id: int) -> Optional[Dict[str, Any]]:
        """获取主题"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM topics WHERE id = ?", (topic_id,))
        row = cursor.fetchone()

        if row:
            return dict(row)
        return None

    def get_user_topics(self, user_id: int, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户主题列表"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT * FROM topics
            WHERE user_id = ? AND is_active = 1
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, (user_id, limit, offset))

        return [dict(row) for row in cursor.fetchall()]

    def attach_document(self, topic_id: int, document_id: int) -> bool:
        """关联文档到主题"""
        # 检查主题是否存在
        topic = self.get_topic(topic_id)
        if not topic:
            return False

        cursor = self.conn.cursor()
        now = datetime.utcnow().isoformat()

        try:
            cursor.execute("""
                INSERT INTO topic_document_links (topic_id, document_id, created_at)
                VALUES (?, ?, ?)
            """, (topic_id, document_id, now))
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            # 已经存在关联
            return True

    def get_topic_documents(self, topic_id: int) -> List[int]:
        """获取主题关联的文档ID列表"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT document_id FROM topic_document_links
            WHERE topic_id = ?
        """, (topic_id,))

        return [row[0] for row in cursor.fetchall()]


# ===== 应用初始化 =====
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global redis_client

    # 启动时初始化
    print("🚀 启动 Topic Service POC...")

    # 初始化数据库
    init_database()
    print("✅ 数据库初始化完成")

    # 初始化 Redis（可选，如果 Redis 不可用则跳过）
    try:
        redis_client = redis.from_url(settings.redis_url, decode_responses=True)
        redis_client.ping()
        print("✅ Redis 连接成功")
    except Exception as e:
        print(f"⚠️ Redis 连接失败，将跳过缓存功能: {e}")
        redis_client = None

    yield

    # 关闭时清理
    print("🛑 关闭 Topic Service POC...")


# ===== FastAPI 应用 =====
app = FastAPI(
    title="Topic Service POC",
    description="主题服务概念验证 - 基于 PROJECT_BLUEPRINT.md 技术栈",
    version="1.0.0",
    lifespan=lifespan
)


# ===== API 路由 =====
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Topic Service POC",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    checks = {
        "database": False,
        "redis": False,
        "manticore": False
    }

    # 检查数据库
    try:
        conn = sqlite3.connect(settings.database_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        conn.close()
        checks["database"] = True
    except Exception:
        pass

    # 检查 Redis
    if redis_client:
        try:
            redis_client.ping()
            checks["redis"] = True
        except Exception:
            pass

    # 检查 Manticore
    connection = manticore_client.get_connection()
    if connection:
        checks["manticore"] = True
        connection.close()

    return {
        "status": "healthy" if all(checks.values()) else "degraded",
        "checks": checks,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/api/v1/topics", response_model=TopicResponse)
async def create_topic(topic_data: TopicCreate, conn: sqlite3.Connection = Depends(get_db)):
    """创建主题"""
    try:
        service = TopicService(conn)
        topic = service.create_topic(topic_data)

        # 构建响应
        response = TopicResponse(
            id=topic["id"],
            title=topic["title"],
            description=topic["description"],
            user_id=topic["user_id"],
            is_active=bool(topic["is_active"]),
            created_at=topic["created_at"],
            updated_at=topic["updated_at"],
            document_count=0
        )

        return response
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/topics/{topic_id}", response_model=TopicResponse)
async def get_topic(topic_id: int, conn: sqlite3.Connection = Depends(get_db)):
    """获取主题详情"""
    service = TopicService(conn)
    topic = service.get_topic(topic_id)

    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")

    # 获取文档数量
    document_count = len(service.get_topic_documents(topic_id))

    return TopicResponse(
        id=topic["id"],
        title=topic["title"],
        description=topic["description"],
        user_id=topic["user_id"],
        is_active=bool(topic["is_active"]),
        created_at=topic["created_at"],
        updated_at=topic["updated_at"],
        document_count=document_count
    )


@app.post("/api/v1/topics/{topic_id}/documents")
async def attach_document(
    topic_id: int,
    doc_data: AttachDocument,
    conn: sqlite3.Connection = Depends(get_db)
):
    """关联文档到主题"""
    service = TopicService(conn)
    success = service.attach_document(topic_id, doc_data.document_id)

    if not success:
        raise HTTPException(status_code=404, detail="主题不存在")

    return {"ok": True, "message": "文档关联成功"}


@app.get("/api/v1/topics/{topic_id}/memory")
async def get_topic_memory(topic_id: int, limit: int = 5):
    """获取主题长期记忆锚点"""
    try:
        # 从 Manticore 检索主题相关的记忆锚点
        results = manticore_client.search_topic_memory(topic_id, limit)
        
        # 转换为响应格式
        memory_results = []
        for result in results:
            memory_results.append(MemorySearchResult(
                id=result.get('id', 0),
                content=result.get('content', ''),
                relevance_score=result.get('relevance_score', 0.0),
                metadata={"topic_id": topic_id}
            ))
        
        return {
            "topic_id": topic_id,
            "memory_anchors": memory_results,
            "total": len(memory_results),
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检索记忆锚点失败: {str(e)}")


@app.get("/api/v1/topics")
async def list_topics(
    user_id: int = 1,
    limit: int = 20,
    offset: int = 0,
    conn: sqlite3.Connection = Depends(get_db)
):
    """获取用户主题列表"""
    service = TopicService(conn)
    topics = service.get_user_topics(user_id, limit, offset)

    # 构建响应
    topic_responses = []
    for topic in topics:
        document_count = len(service.get_topic_documents(topic["id"]))
        topic_responses.append(TopicResponse(
            id=topic["id"],
            title=topic["title"],
            description=topic["description"],
            user_id=topic["user_id"],
            is_active=bool(topic["is_active"]),
            created_at=topic["created_at"],
            updated_at=topic["updated_at"],
            document_count=document_count
        ))

    return {
        "topics": topic_responses,
        "total": len(topic_responses),
        "limit": limit,
        "offset": offset
    }


# ===== 演示脚本 =====
async def demo_workflow():
    """演示完整的工作流程"""
    print("\n🎯 开始 Topic Service POC 演示...")
    
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        # 1. 创建主题
        print("\n1️⃣ 创建主题...")
        topic_data = {
            "title": "Python深度学习",
            "description": "学习Python在深度学习中的应用",
            "user_id": 1
        }
        
        response = await client.post(f"{base_url}/api/v1/topics", json=topic_data)
        if response.status_code == 200:
            topic = response.json()
            topic_id = topic["id"]
            print(f"✅ 主题创建成功: {topic['title']} (ID: {topic_id})")
        else:
            print(f"❌ 主题创建失败: {response.text}")
            return
        
        # 2. 关联文档
        print("\n2️⃣ 关联文档...")
        doc_data = {"document_id": 123}
        response = await client.post(f"{base_url}/api/v1/topics/{topic_id}/documents", json=doc_data)
        if response.status_code == 200:
            print("✅ 文档关联成功")
        else:
            print(f"❌ 文档关联失败: {response.text}")
        
        # 3. 获取主题详情
        print("\n3️⃣ 获取主题详情...")
        response = await client.get(f"{base_url}/api/v1/topics/{topic_id}")
        if response.status_code == 200:
            topic_detail = response.json()
            print(f"✅ 主题详情: {topic_detail['title']}, 文档数: {topic_detail['document_count']}")
        else:
            print(f"❌ 获取主题详情失败: {response.text}")
        
        # 4. 检索记忆锚点
        print("\n4️⃣ 检索主题记忆锚点...")
        response = await client.get(f"{base_url}/api/v1/topics/{topic_id}/memory?limit=3")
        if response.status_code == 200:
            memory_data = response.json()
            print(f"✅ 检索到 {memory_data['total']} 个记忆锚点")
            for anchor in memory_data['memory_anchors']:
                print(f"   - 相关度: {anchor['relevance_score']:.3f}")
        else:
            print(f"❌ 检索记忆锚点失败: {response.text}")
        
        # 5. 获取主题列表
        print("\n5️⃣ 获取主题列表...")
        response = await client.get(f"{base_url}/api/v1/topics?user_id=1&limit=10")
        if response.status_code == 200:
            topics_data = response.json()
            print(f"✅ 用户共有 {topics_data['total']} 个主题")
        else:
            print(f"❌ 获取主题列表失败: {response.text}")
    
    print("\n🎉 POC 演示完成！")


if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动 Topic Service POC...")
    print("📖 API 文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
