#!/bin/bash

# Topic Service POC 演示脚本

set -e

echo "🚀 Topic Service POC 演示启动"
echo "================================"

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️ 建议在虚拟环境中运行"
    echo "创建虚拟环境: python3 -m venv venv"
    echo "激活虚拟环境: source venv/bin/activate"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 检查端口是否被占用
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ 端口 8000 已被占用"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动服务
echo "🎯 启动 Topic Service POC..."
echo "API 文档: http://localhost:8000/docs"
echo "健康检查: http://localhost:8000/health"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务（后台）
python3 main.py &
SERVICE_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 运行测试
echo ""
echo "🧪 运行自动化测试..."
python3 test_poc.py

# 保持服务运行
echo ""
echo "✅ 测试完成！服务继续运行..."
echo "访问 http://localhost:8000/docs 查看 API 文档"
echo "按任意键停止服务..."

read -n 1 -s

# 停止服务
echo "🛑 停止服务..."
kill $SERVICE_PID 2>/dev/null || true

echo "✅ 演示完成！"
