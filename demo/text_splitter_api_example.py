#!/usr/bin/env python3
"""
engines/text_splitter FastAPI 集成示例

展示如何在POC的FastAPI服务中集成 engines/text_splitter 模块。
这个示例模拟了Document Service POC中的实际使用场景。

运行方法:
    python3 demo/text_splitter_api_example.py

然后访问:
    http://localhost:8000/docs - API文档
    http://localhost:8000/split/text - 文本分割接口
    http://localhost:8000/split/document - 文档分割接口
"""

import sys
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from enum import Enum

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from fastapi import FastAPI, HTTPException, UploadFile, File
    from fastapi.responses import JSONResponse
    from pydantic import BaseModel, Field
    import uvicorn
except ImportError:
    print("❌ 需要安装 fastapi 和 uvicorn")
    print("   pip install fastapi uvicorn")
    sys.exit(1)

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document, TextChunk
from engines.text_splitter.config import TextSplitterConfig

# API模型定义
class SplitStrategyType(str, Enum):
    TOKEN_BASED = "token_based"
    CHARACTER_BASED = "character_based"

class TextSplitRequest(BaseModel):
    text: str = Field(..., description="要分割的文本内容")
    strategy: SplitStrategyType = Field(default=SplitStrategyType.TOKEN_BASED, description="分割策略")
    max_tokens: Optional[int] = Field(default=512, description="最大token数（token_based策略）")
    max_chars: Optional[int] = Field(default=1000, description="最大字符数（character_based策略）")

class ChunkResponse(BaseModel):
    chunk_index: int
    content: str
    start_char: int
    end_char: int
    length: int
    token_count: Optional[int]
    preview: str

class SplitResponse(BaseModel):
    total_chunks: int
    strategy_used: str
    chunks: List[ChunkResponse]
    statistics: Dict[str, Any]

class DocumentSplitResponse(BaseModel):
    document_id: str
    title: str
    file_type: str
    total_chunks: int
    strategy_used: str
    chunks: List[ChunkResponse]
    processing_info: Dict[str, Any]

# FastAPI应用
app = FastAPI(
    title="Text Splitter API",
    description="engines/text_splitter 模块的 FastAPI 集成示例",
    version="1.0.0"
)

# 全局文本分割引擎
splitter_engine = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化文本分割引擎"""
    global splitter_engine
    
    # 创建适合POC的配置
    config = TextSplitterConfig(
        default_max_tokens=512,
        default_max_chars=1000,
        markdown_max_chars=800,
        enable_caching=True,
        log_level="INFO"
    )
    
    splitter_engine = TextSplitterEngine(config)
    print("🚀 Text Splitter Engine 初始化完成")

def create_chunk_response(chunk: TextChunk) -> ChunkResponse:
    """将TextChunk转换为API响应格式"""
    return ChunkResponse(
        chunk_index=chunk.chunk_index,
        content=chunk.content,
        start_char=chunk.start_char,
        end_char=chunk.end_char,
        length=chunk.get_length(),
        token_count=chunk.token_count,
        preview=chunk.get_preview(100)
    )

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Text Splitter API",
        "version": "1.0.0",
        "endpoints": {
            "docs": "/docs",
            "text_split": "/split/text",
            "document_split": "/split/document",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    if splitter_engine is None:
        raise HTTPException(status_code=503, detail="Text Splitter Engine not initialized")
    
    stats = splitter_engine.get_stats()
    return {
        "status": "healthy",
        "engine_stats": stats
    }

@app.post("/split/text", response_model=SplitResponse)
async def split_text(request: TextSplitRequest):
    """分割文本接口"""
    if splitter_engine is None:
        raise HTTPException(status_code=503, detail="Text Splitter Engine not initialized")
    
    try:
        # 创建分割策略
        if request.strategy == SplitStrategyType.TOKEN_BASED:
            strategy = TokenBasedStrategy(max_tokens=request.max_tokens or 512)
        else:
            strategy = CharacterBasedStrategy(max_chars=request.max_chars or 1000)
        
        # 执行分割
        chunks = splitter_engine.split_text(request.text, strategy)
        
        # 转换为响应格式
        chunk_responses = [create_chunk_response(chunk) for chunk in chunks]
        
        # 计算统计信息
        statistics = {
            "total_characters": len(request.text),
            "total_chunks": len(chunks),
            "avg_chunk_size": sum(chunk.get_length() for chunk in chunks) / len(chunks) if chunks else 0,
            "total_tokens": sum(chunk.token_count or 0 for chunk in chunks),
            "strategy_params": strategy.get_params()
        }
        
        return SplitResponse(
            total_chunks=len(chunks),
            strategy_used=strategy.name,
            chunks=chunk_responses,
            statistics=statistics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text splitting failed: {str(e)}")

@app.post("/split/document", response_model=DocumentSplitResponse)
async def split_document(
    file: UploadFile = File(...),
    strategy: SplitStrategyType = SplitStrategyType.TOKEN_BASED,
    max_tokens: Optional[int] = 512,
    max_chars: Optional[int] = 1000
):
    """分割上传文档接口"""
    if splitter_engine is None:
        raise HTTPException(status_code=503, detail="Text Splitter Engine not initialized")
    
    try:
        # 读取文件内容
        content = await file.read()
        text = content.decode('utf-8')
        
        # 创建文档对象
        document = Document(
            title=file.filename or "uploaded_document",
            content=text,
            file_type=Path(file.filename or "").suffix.lstrip('.') or "txt",
            size=len(content)
        )
        
        # 创建分割策略
        if strategy == SplitStrategyType.TOKEN_BASED:
            split_strategy = TokenBasedStrategy(max_tokens=max_tokens or 512)
        else:
            split_strategy = CharacterBasedStrategy(max_chars=max_chars or 1000)
        
        # 执行文档分割
        result = splitter_engine.split_document(document, split_strategy)
        
        # 转换为响应格式
        chunk_responses = [create_chunk_response(chunk) for chunk in result.chunks]
        
        # 处理信息
        processing_info = {
            "file_size": len(content),
            "file_type": document.file_type,
            "processing_time": result.processing_time,
            "statistics": result.get_statistics()
        }
        
        return DocumentSplitResponse(
            document_id=result.document_id,
            title=document.title,
            file_type=document.file_type,
            total_chunks=result.total_chunks,
            strategy_used=result.strategy_used,
            chunks=chunk_responses,
            processing_info=processing_info
        )
        
    except UnicodeDecodeError:
        raise HTTPException(status_code=400, detail="File encoding not supported. Please use UTF-8 encoded text files.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Document splitting failed: {str(e)}")

@app.get("/split/strategies")
async def get_available_strategies():
    """获取可用的分割策略"""
    return {
        "strategies": [
            {
                "name": "token_based",
                "description": "基于Token的分割策略，适合需要精确控制token数量的场景",
                "parameters": {
                    "max_tokens": "最大token数，默认512"
                }
            },
            {
                "name": "character_based", 
                "description": "基于字符的分割策略，适合简单的文本分割",
                "parameters": {
                    "max_chars": "最大字符数，默认1000"
                }
            }
        ],
        "default_config": {
            "default_max_tokens": 512,
            "default_max_chars": 1000,
            "markdown_max_chars": 800
        }
    }

@app.post("/split/batch")
async def batch_split_documents(files: List[UploadFile] = File(...)):
    """批量分割文档接口"""
    if splitter_engine is None:
        raise HTTPException(status_code=503, detail="Text Splitter Engine not initialized")
    
    if len(files) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 files allowed per batch")
    
    try:
        documents = []
        
        # 读取所有文件
        for file in files:
            content = await file.read()
            text = content.decode('utf-8')
            
            document = Document(
                title=file.filename or f"document_{len(documents)}",
                content=text,
                file_type=Path(file.filename or "").suffix.lstrip('.') or "txt",
                size=len(content)
            )
            documents.append(document)
        
        # 批量分割
        results = splitter_engine.batch_split(documents)
        
        # 转换为响应格式
        batch_results = []
        for result in results:
            chunk_responses = [create_chunk_response(chunk) for chunk in result.chunks]
            batch_results.append({
                "document_id": result.document_id,
                "total_chunks": result.total_chunks,
                "strategy_used": result.strategy_used,
                "chunks": chunk_responses,
                "error": result.error,
                "statistics": result.get_statistics()
            })
        
        return {
            "total_documents": len(documents),
            "successful_count": sum(1 for r in results if r.error is None),
            "failed_count": sum(1 for r in results if r.error is not None),
            "results": batch_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch splitting failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动 Text Splitter API 服务")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    
    uvicorn.run(
        "text_splitter_api_example:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
