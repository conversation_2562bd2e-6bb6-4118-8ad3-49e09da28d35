#!/usr/bin/env python3
"""
异步Manticore客户端演示脚本

展示POC专用异步Manticore客户端的设计理念和使用方法。
即使没有实际的Manticore服务，也能演示客户端的接口设计和功能特性。

使用方法:
    python3 demo/manticore_client_demo.py

演示内容:
1. 客户端设计理念和架构
2. 异步接口的使用方法
3. POC开发中的集成示例
4. 错误处理和最佳实践
"""

import sys
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_code_example(title: str, code: str):
    """打印代码示例"""
    print(f"\n📝 {title}:")
    print("```python")
    print(code.strip())
    print("```")

async def demonstrate_client_design():
    """演示客户端设计理念"""
    print_separator("异步Manticore客户端设计理念")
    
    print("🎯 设计目标:")
    print("   1. 简化POC开发的复杂性")
    print("   2. 提供异步操作支持")
    print("   3. 统一的错误处理机制")
    print("   4. 灵活的配置管理")
    print("   5. 向后兼容现有同步客户端")
    
    print("\n🏗️ 架构特点:")
    print("   - 基于现有ManticoreClient的异步封装")
    print("   - 支持真正的异步连接池(aiomysql)和同步包装两种模式")
    print("   - 提供上下文管理器支持")
    print("   - 统一的文档处理和向量格式化")
    print("   - 简洁的API设计，适合快速原型开发")

async def demonstrate_basic_usage():
    """演示基础使用方法"""
    print_separator("基础使用方法演示")
    
    # 基础连接示例
    basic_usage = """
from demo.manticore_async_client import AsyncManticoreClient

# 方法1: 基础使用
client = AsyncManticoreClient()
await client.connect()

# 测试连接
if await client.test_connection():
    print("连接成功")

# 获取服务器信息
info = await client.get_server_info()
print(f"服务器版本: {info['version']}")

await client.disconnect()
"""
    print_code_example("基础连接和测试", basic_usage)
    
    # 上下文管理器示例
    context_usage = """
# 方法2: 使用上下文管理器（推荐）
async with AsyncManticoreClient() as client:
    # 自动连接和断开
    info = await client.get_server_info()
    print(f"服务器状态: {info['status']}")

# 方法3: 使用便捷函数
client = await create_poc_client()
# ... 使用客户端
await client.disconnect()
"""
    print_code_example("上下文管理器使用", context_usage)

async def demonstrate_table_operations():
    """演示表操作"""
    print_separator("表操作演示")
    
    table_ops = """
async with AsyncManticoreClient() as client:
    # 创建表
    schema = '''
    id VARCHAR,
    title TEXT,
    content TEXT,
    category VARCHAR,
    embedding FLOAT_VECTOR(384)
    '''
    
    await client.create_table("knowledge_base", schema)
    
    # 检查表是否存在
    exists = await client.table_exists("knowledge_base")
    print(f"表存在: {exists}")
    
    # 删除表
    await client.execute_query("DROP TABLE IF EXISTS knowledge_base", fetch_results=False)
"""
    print_code_example("表创建和管理", table_ops)

async def demonstrate_document_operations():
    """演示文档操作"""
    print_separator("文档操作演示")
    
    doc_ops = """
async with AsyncManticoreClient() as client:
    # 插入单个文档
    document = {
        "id": "doc_001",
        "title": "人工智能基础",
        "content": "人工智能是计算机科学的重要分支...",
        "category": "技术",
        "embedding": [0.1, 0.2, 0.3, ...]  # 384维向量
    }
    
    await client.insert_document("knowledge_base", document)
    
    # 批量插入文档
    documents = [
        {"id": "doc_002", "title": "机器学习", ...},
        {"id": "doc_003", "title": "深度学习", ...},
    ]
    
    count = await client.bulk_insert_documents("knowledge_base", documents)
    print(f"成功插入 {count} 个文档")
"""
    print_code_example("文档插入操作", doc_ops)

async def demonstrate_search_operations():
    """演示搜索操作"""
    print_separator("搜索操作演示")
    
    search_ops = """
async with AsyncManticoreClient() as client:
    # 全文搜索
    results = await client.search_documents(
        table_name="knowledge_base",
        query="人工智能",
        limit=10,
        additional_conditions="category='技术'"
    )
    
    for result in results:
        print(f"标题: {result['title']}")
        print(f"相关度: {result['relevance_score']}")
    
    # 向量搜索
    query_vector = [0.1, 0.2, 0.3, ...]  # 查询向量
    vector_results = await client.vector_search(
        table_name="knowledge_base",
        vector=query_vector,
        limit=5
    )
    
    for result in vector_results:
        print(f"标题: {result['title']}")
        print(f"相似度: {result['similarity_score']}")
"""
    print_code_example("搜索操作", search_ops)

async def demonstrate_poc_integration():
    """演示POC集成"""
    print_separator("POC服务集成演示")
    
    poc_integration = """
# 在Document Service POC中的使用
class DocumentServicePOC:
    def __init__(self):
        self.manticore_client = None
    
    async def startup(self):
        self.manticore_client = await create_poc_client()
        
        # 确保表存在
        schema = '''
        id VARCHAR,
        doc_id VARCHAR,
        chunk_index INT,
        content TEXT,
        embedding FLOAT_VECTOR(1536)
        '''
        await self.manticore_client.create_table("doc_chunks", schema)
    
    async def store_document_chunks(self, doc_id: str, chunks: List[Dict]):
        '''存储文档分块到Manticore'''
        documents = []
        for i, chunk in enumerate(chunks):
            doc = {
                "id": f"{doc_id}_{i}",
                "doc_id": doc_id,
                "chunk_index": i,
                "content": chunk["content"],
                "embedding": chunk["embedding"]
            }
            documents.append(doc)
        
        count = await self.manticore_client.bulk_insert_documents("doc_chunks", documents)
        return count
    
    async def search_similar_chunks(self, query_vector: List[float], limit: int = 5):
        '''搜索相似的文档块'''
        return await self.manticore_client.vector_search(
            "doc_chunks", query_vector, limit
        )
"""
    print_code_example("POC服务集成", poc_integration)

async def demonstrate_error_handling():
    """演示错误处理"""
    print_separator("错误处理和最佳实践")
    
    error_handling = """
from manticore_search.utils.exceptions import ConnectionError, QueryError

async def robust_manticore_operation():
    try:
        async with AsyncManticoreClient() as client:
            # 执行操作
            results = await client.search_documents("table", "query")
            return results
            
    except ConnectionError as e:
        logger.error(f"Manticore连接失败: {e}")
        # 可以实现重试逻辑或降级处理
        return []
        
    except QueryError as e:
        logger.error(f"查询执行失败: {e}")
        # 记录错误并返回空结果
        return []
        
    except Exception as e:
        logger.error(f"未知错误: {e}")
        return []

# 配置管理最佳实践
def create_manticore_client_with_config():
    from manticore_search.utils.config import Settings
    
    # 自定义配置
    settings = Settings(
        manticore_host="localhost",
        manticore_port=9306,
        connection_timeout=30,
        vector_dimensions=1536
    )
    
    return AsyncManticoreClient(settings)
"""
    print_code_example("错误处理和配置", error_handling)

async def demonstrate_performance_tips():
    """演示性能优化建议"""
    print_separator("性能优化建议")
    
    print("🚀 性能优化要点:")
    print("   1. 使用连接池: 配置合适的connection_pool_size")
    print("   2. 批量操作: 优先使用bulk_insert_documents")
    print("   3. 异步并发: 合理使用asyncio.gather进行并发操作")
    print("   4. 向量维度: 根据实际需求选择合适的向量维度")
    print("   5. 索引优化: 为常用查询字段创建适当的索引")
    
    performance_tips = """
# 并发批量插入
async def concurrent_bulk_insert(client, table_name, all_documents):
    batch_size = 100
    batches = [all_documents[i:i+batch_size] 
               for i in range(0, len(all_documents), batch_size)]
    
    # 并发执行多个批次
    tasks = [
        client.bulk_insert_documents(table_name, batch)
        for batch in batches
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    total_inserted = sum(r for r in results if isinstance(r, int))
    return total_inserted

# 连接池配置
settings = Settings(
    connection_pool_size=20,  # 根据并发需求调整
    connection_timeout=30,
    read_timeout=60
)
client = AsyncManticoreClient(settings)
"""
    print_code_example("性能优化示例", performance_tips)

async def main():
    """主演示函数"""
    print("🚀 异步Manticore客户端设计演示")
    print("=" * 60)
    
    # 执行各种演示
    await demonstrate_client_design()
    await demonstrate_basic_usage()
    await demonstrate_table_operations()
    await demonstrate_document_operations()
    await demonstrate_search_operations()
    await demonstrate_poc_integration()
    await demonstrate_error_handling()
    await demonstrate_performance_tips()
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("\n💡 异步Manticore客户端的优势:")
    print("   1. 简化的API设计，降低POC开发复杂度")
    print("   2. 异步操作支持，提高并发性能")
    print("   3. 统一的错误处理和日志记录")
    print("   4. 灵活的配置管理和环境适配")
    print("   5. 向后兼容，可以逐步迁移到真正的异步实现")
    
    print("\n🔧 在POC中的使用建议:")
    print("   1. 优先使用上下文管理器确保资源正确释放")
    print("   2. 合理配置连接池大小和超时时间")
    print("   3. 实现适当的错误处理和重试机制")
    print("   4. 使用批量操作提高性能")
    print("   5. 根据实际需求选择合适的向量维度")

if __name__ == "__main__":
    asyncio.run(main())
