#!/usr/bin/env python3
"""
engines/text_splitter 使用示例

展示如何在POC项目中使用 engines/text_splitter 模块进行文档分割。
这个示例演示了实际POC开发中的常见使用场景。

使用方法:
    python3 demo/text_splitter_usage_example.py

功能演示:
1. 基础文本分割
2. 不同策略的选择
3. 配置自定义
4. 与POC服务集成的模拟
"""

import sys
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document, TextChunk, SplitResult
from engines.text_splitter.config import TextSplitterConfig

class DocumentPOCService:
    """模拟Document Service POC中的文本分割使用"""
    
    def __init__(self):
        # 创建适合POC的配置
        self.config = TextSplitterConfig(
            default_max_tokens=512,  # POC中使用较小的块
            default_max_chars=1000,
            markdown_max_chars=800,
            enable_caching=True
        )
        self.splitter_engine = TextSplitterEngine(self.config)
        print(f"📝 Document POC Service 初始化完成")
        print(f"   配置: max_tokens={self.config.default_max_tokens}, max_chars={self.config.default_max_chars}")
    
    async def process_uploaded_document(self, file_path: str, file_type: str = "txt") -> Dict[str, Any]:
        """模拟处理上传文档的完整流程"""
        print(f"\n🔄 处理文档: {file_path}")
        
        # 1. 读取文档内容
        try:
            content = Path(file_path).read_text(encoding='utf-8')
            print(f"   ✅ 读取文档成功，长度: {len(content)} 字符")
        except Exception as e:
            print(f"   ❌ 读取文档失败: {e}")
            return {"error": str(e)}
        
        # 2. 创建文档对象
        document = Document(
            title=Path(file_path).stem,
            content=content,
            file_type=file_type,
            size=len(content.encode('utf-8'))
        )
        
        # 3. 选择分割策略
        strategy = self._choose_strategy(document)
        print(f"   📊 选择策略: {strategy.name}")
        
        # 4. 执行分割
        try:
            result = self.splitter_engine.split_document(document, strategy)
            print(f"   ✅ 分割完成: {result.total_chunks} 个块")
        except Exception as e:
            print(f"   ❌ 分割失败: {e}")
            return {"error": str(e)}
        
        # 5. 模拟保存到数据库
        chunk_metadata = await self._save_chunks_to_db(result)
        
        # 6. 模拟发布到任务队列
        task_ids = await self._publish_embedding_tasks(result.chunks)
        
        return {
            "document_id": result.document_id,
            "total_chunks": result.total_chunks,
            "chunk_metadata": chunk_metadata,
            "embedding_task_ids": task_ids,
            "strategy_used": result.strategy_used,
            "statistics": result.get_statistics()
        }
    
    def _choose_strategy(self, document: Document) -> Any:
        """根据文档类型选择最佳分割策略"""
        if document.file_type.lower() in ['md', 'markdown']:
            return CharacterBasedStrategy(max_chars=self.config.markdown_max_chars)
        elif document.get_char_count() > 5000:
            # 大文档使用token-based策略
            return TokenBasedStrategy(max_tokens=self.config.default_max_tokens)
        else:
            # 小文档使用character-based策略
            return CharacterBasedStrategy(max_chars=self.config.default_max_chars)
    
    async def _save_chunks_to_db(self, result: SplitResult) -> List[Dict[str, Any]]:
        """模拟保存chunks到PostgreSQL数据库"""
        print(f"   💾 模拟保存 {len(result.chunks)} 个chunks到数据库...")
        
        chunk_metadata = []
        for chunk in result.chunks:
            metadata = {
                "chunk_id": f"{result.document_id}_{chunk.chunk_index}",
                "document_id": result.document_id,
                "chunk_index": chunk.chunk_index,
                "content_preview": chunk.get_preview(50),
                "char_count": chunk.get_length(),
                "token_count": chunk.token_count,
                "start_char": chunk.start_char,
                "end_char": chunk.end_char
            }
            chunk_metadata.append(metadata)
        
        # 模拟数据库延迟
        await asyncio.sleep(0.1)
        print(f"   ✅ 数据库保存完成")
        
        return chunk_metadata
    
    async def _publish_embedding_tasks(self, chunks: List[TextChunk]) -> List[str]:
        """模拟发布embedding任务到Dramatiq队列"""
        print(f"   📤 模拟发布 {len(chunks)} 个embedding任务...")
        
        task_ids = []
        for chunk in chunks:
            task_id = f"embed_task_{chunk.chunk_index}_{hash(chunk.content) % 10000}"
            task_payload = {
                "task_id": task_id,
                "chunk_content": chunk.content,
                "chunk_index": chunk.chunk_index,
                "token_count": chunk.token_count
            }
            # 在实际POC中，这里会调用 dramatiq.send_task()
            task_ids.append(task_id)
        
        # 模拟任务发布延迟
        await asyncio.sleep(0.05)
        print(f"   ✅ 任务发布完成")
        
        return task_ids

def create_sample_documents():
    """创建示例文档用于测试"""
    sample_dir = Path("demo/samples")
    sample_dir.mkdir(exist_ok=True)
    
    # 创建技术文档示例
    tech_doc = """
# 深度学习基础

## 什么是深度学习

深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。这种方法在图像识别、自然语言处理和语音识别等领域取得了突破性进展。

## 神经网络架构

### 前馈神经网络
前馈神经网络是最基本的神经网络类型，信息只在一个方向上流动，从输入层到输出层。

### 卷积神经网络
卷积神经网络（CNN）特别适用于处理图像数据，通过卷积层、池化层和全连接层的组合来提取特征。

### 循环神经网络
循环神经网络（RNN）能够处理序列数据，如文本和时间序列，通过记忆机制来捕捉序列中的依赖关系。

## 训练过程

深度学习模型的训练包括前向传播、损失计算、反向传播和参数更新等步骤。这个过程需要大量的数据和计算资源。

## 应用领域

深度学习在以下领域有广泛应用：
- 计算机视觉：图像分类、物体检测、人脸识别
- 自然语言处理：机器翻译、情感分析、文本生成
- 语音技术：语音识别、语音合成
- 推荐系统：个性化推荐、内容过滤
"""
    
    tech_file = sample_dir / "deep_learning.md"
    tech_file.write_text(tech_doc.strip(), encoding='utf-8')
    
    # 创建普通文本示例
    text_doc = """
人工智能的发展历程可以追溯到20世纪50年代。当时，科学家们开始探索如何让机器模拟人类的思维过程。

早期的人工智能研究主要集中在符号推理和专家系统上。这些系统通过预定义的规则来解决特定领域的问题。

随着计算能力的提升和大数据的出现，机器学习方法逐渐成为主流。特别是深度学习的兴起，使得人工智能在许多任务上达到了人类水平的性能。

今天，人工智能已经渗透到我们生活的各个方面，从智能手机的语音助手到自动驾驶汽车，从医疗诊断到金融分析。

未来，人工智能将继续发展，可能会在更多领域实现突破，同时也需要我们思考如何确保AI技术的安全性和伦理性。
"""
    
    text_file = sample_dir / "ai_history.txt"
    text_file.write_text(text_doc.strip(), encoding='utf-8')
    
    return [str(tech_file), str(text_file)]

async def demonstrate_basic_usage():
    """演示基础使用方法"""
    print("🎯 演示1: 基础文本分割")
    
    # 创建引擎
    engine = TextSplitterEngine()
    
    # 简单文本分割
    text = "这是一个测试文本。它包含多个句子。我们将使用text_splitter来分割它。每个句子都有不同的内容。"
    
    # Token-based分割
    token_strategy = TokenBasedStrategy(max_tokens=20)
    chunks = engine.split_text(text, token_strategy)
    
    print(f"   Token-based分割结果: {len(chunks)} 个块")
    for i, chunk in enumerate(chunks):
        print(f"     块{i+1}: {chunk.get_preview(30)}")

async def demonstrate_poc_integration():
    """演示POC集成使用"""
    print("\n🎯 演示2: POC服务集成")
    
    # 创建示例文档
    sample_files = create_sample_documents()
    
    # 创建POC服务
    poc_service = DocumentPOCService()
    
    # 处理每个文档
    for file_path in sample_files:
        file_type = "markdown" if file_path.endswith('.md') else "txt"
        result = await poc_service.process_uploaded_document(file_path, file_type)
        
        if "error" not in result:
            print(f"   📊 处理结果摘要:")
            print(f"     文档ID: {result['document_id']}")
            print(f"     总块数: {result['total_chunks']}")
            print(f"     使用策略: {result['strategy_used']}")
            print(f"     任务数: {len(result['embedding_task_ids'])}")

async def demonstrate_custom_config():
    """演示自定义配置"""
    print("\n🎯 演示3: 自定义配置")
    
    # 创建针对不同场景的配置
    configs = {
        "小块配置": TextSplitterConfig(
            default_max_tokens=100,
            default_max_chars=200
        ),
        "大块配置": TextSplitterConfig(
            default_max_tokens=1000,
            default_max_chars=2000
        ),
        "Markdown优化": TextSplitterConfig(
            markdown_max_chars=500,
            markdown_preserve_headers=True
        )
    }
    
    text = "人工智能是计算机科学的一个分支。" * 20  # 重复文本用于测试
    
    for config_name, config in configs.items():
        engine = TextSplitterEngine(config)
        strategy = TokenBasedStrategy(max_tokens=config.default_max_tokens)
        chunks = engine.split_text(text, strategy)
        
        print(f"   {config_name}: {len(chunks)} 个块")
        if chunks:
            print(f"     平均块大小: {sum(len(c.content) for c in chunks) / len(chunks):.1f} 字符")

async def main():
    """主演示函数"""
    print("🚀 engines/text_splitter 使用示例演示")
    print("=" * 60)
    
    # 执行各种演示
    await demonstrate_basic_usage()
    await demonstrate_poc_integration()
    await demonstrate_custom_config()
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("\n💡 在POC开发中的使用要点:")
    print("   1. 根据文档类型选择合适的分割策略")
    print("   2. 配置适合POC需求的块大小（通常512-1000 tokens）")
    print("   3. 将分割结果保存到数据库并发布embedding任务")
    print("   4. 利用批量处理功能提高性能")
    print("   5. 实现适当的错误处理和日志记录")

if __name__ == "__main__":
    asyncio.run(main())
