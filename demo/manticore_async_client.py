#!/usr/bin/env python3
"""
POC专用异步Manticore客户端封装

基于蓝图要求，使用asyncpg和manticore-async驱动创建适用于POC的异步客户端。
提供简洁易用的接口，支持基本的连接、建表、插入、搜索和向量搜索功能。

技术栈:
- asyncpg: PostgreSQL异步驱动
- manticoresearch-python: 官方Manticore Search Python客户端
- 支持HTTP API和MySQL协议两种连接方式

使用方法:
    from demo.manticore_async_client import AsyncManticoreClient

    client = AsyncManticoreClient()
    await client.connect()
    await client.create_table("docs", schema)
    await client.insert_document("docs", document)
    results = await client.search("docs", "query")
"""

import sys
import asyncio
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    import aiomysql
    HAS_AIOMYSQL = True
except ImportError:
    HAS_AIOMYSQL = False
    print("⚠️  aiomysql未安装，将使用同步客户端的异步包装")

try:
    from manticore_search.clients.manticore_client import ManticoreClient
    from manticore_search.utils.config import Settings, get_settings
    from manticore_search.utils.logger import get_client_logger
    from manticore_search.utils.exceptions import ConnectionError, QueryError
    HAS_MANTICORE_CLIENT = True
except ImportError:
    HAS_MANTICORE_CLIENT = False
    print("❌ 无法导入manticore_search模块")

    # 创建简单的配置类作为fallback
    class Settings:
        def __init__(self):
            self.manticore_host = "localhost"
            self.manticore_port = 9306
            self.manticore_user = ""
            self.manticore_password = ""
            self.manticore_charset = "utf8mb4"
            self.connection_timeout = 30
            self.connection_pool_size = 10

    def get_settings():
        return Settings()

    class ConnectionError(Exception):
        pass

    class QueryError(Exception):
        pass

class AsyncManticoreClient:
    """POC专用异步Manticore客户端"""

    def __init__(self, settings: Optional['Settings'] = None):
        """初始化异步客户端"""
        self.settings = settings or get_settings()
        if HAS_MANTICORE_CLIENT:
            self.logger = get_client_logger()
        else:
            self.logger = None
        self._pool = None
        self._sync_client = None
        self._connected = False
        
        # 如果没有aiomysql，使用同步客户端的异步包装
        if not HAS_AIOMYSQL and HAS_MANTICORE_CLIENT:
            self._sync_client = ManticoreClient(self.settings)
            self._use_sync_wrapper = True
        else:
            self._use_sync_wrapper = False
    
    async def connect(self) -> bool:
        """建立连接"""
        try:
            if self._use_sync_wrapper:
                # 使用同步客户端测试连接
                result = await asyncio.get_event_loop().run_in_executor(
                    None, self._sync_client.test_connection
                )
                self._connected = result
                if self.logger:
                    self.logger.info(f"异步客户端连接成功 (使用同步包装): {self.settings.manticore_host}:{self.settings.manticore_port}")
                return result
            
            elif HAS_AIOMYSQL:
                # 使用真正的异步连接
                self._pool = await aiomysql.create_pool(
                    host=self.settings.manticore_host,
                    port=self.settings.manticore_port,
                    user=self.settings.manticore_user,
                    password=self.settings.manticore_password,
                    charset=self.settings.manticore_charset,
                    autocommit=True,
                    connect_timeout=self.settings.connection_timeout,
                    minsize=1,
                    maxsize=self.settings.connection_pool_size
                )
                self._connected = True
                if self.logger:
                    self.logger.info(f"异步连接池创建成功: {self.settings.manticore_host}:{self.settings.manticore_port}")
                return True
            
            else:
                print("❌ 无可用的Manticore客户端实现")
                return False
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"连接失败: {e}")
            else:
                print(f"❌ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self._pool:
                self._pool.close()
                await self._pool.wait_closed()
                self._pool = None
            
            if self._sync_client:
                await asyncio.get_event_loop().run_in_executor(
                    None, self._sync_client.close
                )
            
            self._connected = False
            if self.logger:
                self.logger.info("异步客户端连接已断开")
                
        except Exception as e:
            if self.logger:
                self.logger.warning(f"断开连接时出错: {e}")
    
    async def execute_query(
        self, 
        sql: str, 
        params: Optional[tuple] = None,
        fetch_results: bool = True
    ) -> Optional[List[Dict[str, Any]]]:
        """执行SQL查询"""
        if not self._connected:
            raise ConnectionError("客户端未连接")
        
        start_time = time.time()
        
        try:
            if self._use_sync_wrapper:
                # 使用同步客户端的异步包装
                result = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: self._sync_client.execute_query(sql, params, fetch_results)
                )
                return result
            
            elif self._pool:
                # 使用真正的异步查询
                async with self._pool.acquire() as conn:
                    async with conn.cursor(aiomysql.DictCursor) as cursor:
                        await cursor.execute(sql, params)
                        
                        if fetch_results:
                            results = await cursor.fetchall()
                            execution_time = (time.time() - start_time) * 1000
                            if self.logger:
                                self.logger.debug(f"异步查询完成，返回 {len(results)} 条记录，耗时 {execution_time:.2f}ms")
                            return results
                        else:
                            execution_time = (time.time() - start_time) * 1000
                            if self.logger:
                                self.logger.debug(f"异步执行完成，耗时 {execution_time:.2f}ms")
                            return None
            
            else:
                raise ConnectionError("无可用连接")
                
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            if self.logger:
                self.logger.error(f"异步查询失败，耗时 {execution_time:.2f}ms: {e}")
            else:
                print(f"❌ 查询失败: {e}")
            raise QueryError(f"查询执行失败: {e}")
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            await self.execute_query("SHOW TABLES")
            return True
        except Exception:
            return False
    
    async def create_table(self, table_name: str, schema: str) -> bool:
        """创建表"""
        try:
            # 删除已存在的表
            await self.execute_query(f"DROP TABLE IF EXISTS {table_name}", fetch_results=False)
            
            # 创建新表
            create_sql = f"CREATE TABLE {table_name} ({schema}) engine='columnar'"
            await self.execute_query(create_sql, fetch_results=False)
            
            if self.logger:
                self.logger.info(f"异步创建表 '{table_name}' 成功")
            else:
                print(f"✅ 创建表 '{table_name}' 成功")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"创建表 '{table_name}' 失败: {e}")
            else:
                print(f"❌ 创建表 '{table_name}' 失败: {e}")
            raise
    
    async def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            results = await self.execute_query("SHOW TABLES")
            if results:
                table_names = [row.get('Index', '') for row in results]
                return table_name in table_names
            return False
        except Exception:
            return False
    
    async def insert_document(self, table_name: str, document: Dict[str, Any]) -> bool:
        """插入单个文档"""
        try:
            # 处理向量字段格式
            processed_doc = self._process_document_for_insert(document)
            
            # 构建插入SQL
            fields = list(processed_doc.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({placeholders})"
            values = tuple(processed_doc.values())
            
            await self.execute_query(sql, values, fetch_results=False)
            
            if self.logger:
                self.logger.debug(f"异步插入文档到表 '{table_name}' 成功")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"插入文档到表 '{table_name}' 失败: {e}")
            else:
                print(f"❌ 插入文档失败: {e}")
            raise
    
    async def bulk_insert_documents(
        self, 
        table_name: str, 
        documents: List[Dict[str, Any]]
    ) -> int:
        """批量插入文档"""
        if not documents:
            return 0
        
        success_count = 0
        
        for doc in documents:
            try:
                await self.insert_document(table_name, doc)
                success_count += 1
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"批量插入中单个文档失败: {e}")
                continue
        
        if self.logger:
            self.logger.info(f"异步批量插入完成: {success_count}/{len(documents)} 成功")
        else:
            print(f"✅ 批量插入完成: {success_count}/{len(documents)} 成功")
        
        return success_count
    
    async def search_documents(
        self, 
        table_name: str, 
        query: str, 
        limit: int = 20,
        offset: int = 0,
        additional_conditions: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            # 构建搜索SQL
            sql = f"""
            SELECT *, WEIGHT() as relevance_score 
            FROM {table_name} 
            WHERE MATCH(%s)
            """
            
            params = [query]
            
            # 添加额外条件
            if additional_conditions:
                sql += f" AND {additional_conditions}"
            
            # 添加排序和分页
            sql += " ORDER BY relevance_score DESC"
            sql += f" LIMIT {offset}, {limit}"
            
            results = await self.execute_query(sql, tuple(params))
            return results or []
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"搜索文档失败: {e}")
            else:
                print(f"❌ 搜索失败: {e}")
            raise
    
    async def vector_search(
        self,
        table_name: str,
        vector: List[float],
        limit: int = 10,
        additional_conditions: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            # 将向量转换为Manticore格式
            vector_str = '(' + ','.join(map(str, vector)) + ')'
            
            # 构建向量搜索SQL
            sql = f"""
            SELECT *, DOT(embedding, {vector_str}) as similarity_score
            FROM {table_name}
            """
            
            params = []
            
            # 添加额外条件
            if additional_conditions:
                sql += f" WHERE {additional_conditions}"
            
            # 添加排序和分页
            sql += " ORDER BY similarity_score DESC"
            sql += f" LIMIT {limit}"
            
            results = await self.execute_query(sql, tuple(params))
            return results or []
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"向量搜索失败: {e}")
            else:
                print(f"❌ 向量搜索失败: {e}")
            raise
    
    def _process_document_for_insert(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """处理文档数据以适配Manticore插入格式"""
        processed_doc = document.copy()
        
        # 处理embedding字段
        if 'embedding' in processed_doc and processed_doc['embedding'] is not None:
            embedding = processed_doc['embedding']
            
            # 如果是列表格式，转换为Manticore float_vector格式
            if isinstance(embedding, list):
                vector_str = '(' + ','.join(map(str, embedding)) + ')'
                processed_doc['embedding'] = vector_str
            elif isinstance(embedding, str) and not embedding.startswith('('):
                # 如果是字符串但不是正确格式，尝试解析并重新格式化
                try:
                    import ast
                    vector_list = ast.literal_eval(embedding)
                    if isinstance(vector_list, list):
                        vector_str = '(' + ','.join(map(str, vector_list)) + ')'
                        processed_doc['embedding'] = vector_str
                except:
                    if self.logger:
                        self.logger.warning(f"无法解析向量字符串格式: {embedding[:50]}...")
        
        return processed_doc
    
    async def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        try:
            results = await self.execute_query("SHOW VERSION")
            if results:
                return {
                    "version": results[0].get("Value", "Unknown"),
                    "status": "connected",
                    "async": True
                }
            return {"status": "connected", "version": "Unknown", "async": True}
        except Exception as e:
            return {"status": "error", "error": str(e), "async": True}
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()

# 便捷函数
async def create_poc_client(settings: Optional['Settings'] = None) -> AsyncManticoreClient:
    """创建POC专用的异步Manticore客户端"""
    client = AsyncManticoreClient(settings)
    await client.connect()
    return client

# 示例用法
async def example_usage():
    """使用示例"""
    print("🚀 异步Manticore客户端使用示例")
    
    try:
        # 创建客户端
        async with AsyncManticoreClient() as client:
            # 测试连接
            if await client.test_connection():
                print("✅ 连接测试成功")
                
                # 获取服务器信息
                info = await client.get_server_info()
                print(f"📊 服务器信息: {info}")
                
                # 创建测试表
                schema = """
                id VARCHAR,
                title TEXT,
                content TEXT,
                embedding FLOAT_VECTOR(384)
                """
                
                await client.create_table("test_docs", schema)
                
                # 插入测试文档
                doc = {
                    "id": "doc1",
                    "title": "测试文档",
                    "content": "这是一个测试文档的内容",
                    "embedding": [0.1] * 384  # 模拟向量
                }
                
                await client.insert_document("test_docs", doc)
                print("✅ 文档插入成功")
                
                # 搜索测试
                results = await client.search_documents("test_docs", "测试")
                print(f"🔍 搜索结果: {len(results)} 条")
                
            else:
                print("❌ 连接测试失败")
                
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(example_usage())
