#!/usr/bin/env python3
"""
异步Manticore客户端测试脚本

验证demo/manticore_async_client.py的功能完整性，确保可以在POC中正常使用。

使用方法:
    python3 demo/test_manticore_client.py

验证内容:
1. 异步客户端连接和初始化
2. 表创建和管理功能
3. 文档插入和批量插入
4. 全文搜索功能
5. 向量搜索功能
6. 错误处理和连接管理
"""

import sys
import asyncio
import random
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from demo.manticore_async_client import AsyncManticoreClient, create_poc_client
    print("✅ 成功导入异步Manticore客户端")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def generate_sample_embedding(dim: int = 384) -> List[float]:
    """生成示例向量"""
    return [random.random() for _ in range(dim)]

async def test_connection_and_initialization():
    """测试连接和初始化"""
    print_separator("测试1: 连接和初始化")
    
    try:
        # 测试基本初始化
        client = AsyncManticoreClient()
        print("✅ 客户端初始化成功")
        
        # 测试连接
        connected = await client.connect()
        if connected:
            print("✅ 异步连接建立成功")
            
            # 测试连接状态
            is_connected = await client.test_connection()
            print(f"✅ 连接状态测试: {'成功' if is_connected else '失败'}")
            
            # 获取服务器信息
            info = await client.get_server_info()
            print(f"✅ 服务器信息: {info}")
            
            await client.disconnect()
            print("✅ 连接断开成功")
            return True
        else:
            print("❌ 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

async def test_table_operations():
    """测试表操作"""
    print_separator("测试2: 表操作")
    
    try:
        async with AsyncManticoreClient() as client:
            # 测试表创建
            table_name = "test_poc_table"
            schema = """
            id VARCHAR,
            title TEXT,
            content TEXT,
            category VARCHAR,
            embedding FLOAT_VECTOR(384)
            """
            
            success = await client.create_table(table_name, schema)
            if success:
                print(f"✅ 表 '{table_name}' 创建成功")
            
            # 测试表存在性检查
            exists = await client.table_exists(table_name)
            print(f"✅ 表存在性检查: {'存在' if exists else '不存在'}")
            
            # 清理测试表
            await client.execute_query(f"DROP TABLE IF EXISTS {table_name}", fetch_results=False)
            print(f"✅ 测试表清理完成")
            
            return True
            
    except Exception as e:
        print(f"❌ 表操作测试失败: {e}")
        return False

async def test_document_insertion():
    """测试文档插入"""
    print_separator("测试3: 文档插入")
    
    try:
        async with AsyncManticoreClient() as client:
            table_name = "test_docs_insert"
            schema = """
            id VARCHAR,
            title TEXT,
            content TEXT,
            category VARCHAR,
            embedding FLOAT_VECTOR(384)
            """
            
            # 创建测试表
            await client.create_table(table_name, schema)
            
            # 测试单个文档插入
            doc = {
                "id": "doc_001",
                "title": "人工智能基础",
                "content": "人工智能是计算机科学的一个分支，它企图了解智能的实质。",
                "category": "技术",
                "embedding": generate_sample_embedding(384)
            }
            
            success = await client.insert_document(table_name, doc)
            if success:
                print("✅ 单个文档插入成功")
            
            # 测试批量文档插入
            docs = []
            for i in range(5):
                docs.append({
                    "id": f"doc_{i:03d}",
                    "title": f"测试文档 {i+1}",
                    "content": f"这是第 {i+1} 个测试文档的内容，包含一些示例文本。",
                    "category": "测试",
                    "embedding": generate_sample_embedding(384)
                })
            
            inserted_count = await client.bulk_insert_documents(table_name, docs)
            print(f"✅ 批量插入完成: {inserted_count}/{len(docs)} 成功")
            
            # 验证插入结果
            results = await client.execute_query(f"SELECT COUNT(*) as total FROM {table_name}")
            total_docs = results[0]['total'] if results else 0
            print(f"✅ 表中总文档数: {total_docs}")
            
            # 清理
            await client.execute_query(f"DROP TABLE IF EXISTS {table_name}", fetch_results=False)
            
            return True
            
    except Exception as e:
        print(f"❌ 文档插入测试失败: {e}")
        return False

async def test_text_search():
    """测试全文搜索"""
    print_separator("测试4: 全文搜索")
    
    try:
        async with AsyncManticoreClient() as client:
            table_name = "test_search"
            schema = """
            id VARCHAR,
            title TEXT,
            content TEXT,
            category VARCHAR,
            embedding FLOAT_VECTOR(384)
            """
            
            # 创建测试表并插入数据
            await client.create_table(table_name, schema)
            
            # 插入测试文档
            test_docs = [
                {
                    "id": "ai_001",
                    "title": "人工智能概述",
                    "content": "人工智能是计算机科学的一个重要分支，研究如何让机器模拟人类智能。",
                    "category": "AI",
                    "embedding": generate_sample_embedding(384)
                },
                {
                    "id": "ml_001", 
                    "title": "机器学习基础",
                    "content": "机器学习是人工智能的一个子集，通过算法让计算机从数据中学习。",
                    "category": "ML",
                    "embedding": generate_sample_embedding(384)
                },
                {
                    "id": "dl_001",
                    "title": "深度学习入门",
                    "content": "深度学习使用神经网络来模拟人脑的工作方式，在图像识别等领域表现出色。",
                    "category": "DL", 
                    "embedding": generate_sample_embedding(384)
                }
            ]
            
            await client.bulk_insert_documents(table_name, test_docs)
            
            # 测试不同的搜索查询
            search_queries = [
                "人工智能",
                "机器学习",
                "神经网络",
                "计算机"
            ]
            
            for query in search_queries:
                results = await client.search_documents(table_name, query, limit=5)
                print(f"🔍 搜索 '{query}': {len(results)} 条结果")
                
                for result in results[:2]:  # 只显示前2条
                    score = result.get('relevance_score', 0)
                    title = result.get('title', 'N/A')
                    print(f"   - {title} (相关度: {score})")
            
            # 清理
            await client.execute_query(f"DROP TABLE IF EXISTS {table_name}", fetch_results=False)
            
            return True
            
    except Exception as e:
        print(f"❌ 全文搜索测试失败: {e}")
        return False

async def test_vector_search():
    """测试向量搜索"""
    print_separator("测试5: 向量搜索")
    
    try:
        async with AsyncManticoreClient() as client:
            table_name = "test_vector_search"
            schema = """
            id VARCHAR,
            title TEXT,
            content TEXT,
            embedding FLOAT_VECTOR(384)
            """
            
            # 创建测试表
            await client.create_table(table_name, schema)
            
            # 插入带向量的测试文档
            base_vector = [0.5] * 384  # 基准向量
            
            test_docs = [
                {
                    "id": "vec_001",
                    "title": "相似文档1",
                    "content": "这是一个与基准向量相似的文档",
                    "embedding": [v + random.uniform(-0.1, 0.1) for v in base_vector]  # 相似向量
                },
                {
                    "id": "vec_002",
                    "title": "相似文档2", 
                    "content": "这也是一个与基准向量相似的文档",
                    "embedding": [v + random.uniform(-0.15, 0.15) for v in base_vector]  # 相似向量
                },
                {
                    "id": "vec_003",
                    "title": "不同文档",
                    "content": "这是一个与基准向量差异较大的文档",
                    "embedding": generate_sample_embedding(384)  # 随机向量
                }
            ]
            
            await client.bulk_insert_documents(table_name, test_docs)
            
            # 执行向量搜索
            query_vector = [v + random.uniform(-0.05, 0.05) for v in base_vector]  # 查询向量
            results = await client.vector_search(table_name, query_vector, limit=3)
            
            print(f"🎯 向量搜索结果: {len(results)} 条")
            for result in results:
                similarity = result.get('similarity_score', 0)
                title = result.get('title', 'N/A')
                print(f"   - {title} (相似度: {similarity:.4f})")
            
            # 清理
            await client.execute_query(f"DROP TABLE IF EXISTS {table_name}", fetch_results=False)
            
            return True
            
    except Exception as e:
        print(f"❌ 向量搜索测试失败: {e}")
        return False

async def test_context_manager():
    """测试上下文管理器"""
    print_separator("测试6: 上下文管理器")
    
    try:
        # 测试异步上下文管理器
        async with AsyncManticoreClient() as client:
            info = await client.get_server_info()
            print(f"✅ 上下文管理器测试成功: {info.get('status', 'unknown')}")
        
        # 测试便捷函数
        client = await create_poc_client()
        info = await client.get_server_info()
        print(f"✅ 便捷函数测试成功: {info.get('status', 'unknown')}")
        await client.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文管理器测试失败: {e}")
        return False

async def test_error_handling():
    """测试错误处理"""
    print_separator("测试7: 错误处理")
    
    try:
        client = AsyncManticoreClient()
        
        # 测试未连接状态下的操作
        try:
            await client.execute_query("SHOW TABLES")
            print("⚠️  未连接状态下执行查询应该失败")
        except Exception as e:
            print(f"✅ 正确捕获未连接错误: {type(e).__name__}")
        
        # 连接后测试错误SQL
        await client.connect()
        try:
            await client.execute_query("INVALID SQL STATEMENT")
            print("⚠️  无效SQL应该失败")
        except Exception as e:
            print(f"✅ 正确捕获SQL错误: {type(e).__name__}")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始验证异步Manticore客户端")
    
    # 执行所有测试
    tests = [
        test_connection_and_initialization,
        test_table_operations,
        test_document_insertion,
        test_text_search,
        test_vector_search,
        test_context_manager,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    # 输出总结
    print_separator("测试总结")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    
    if passed == total:
        print("🎉 所有测试通过！异步Manticore客户端可用性验证成功")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
