services:
  manticore:
    image: manticoresearch/manticore
    container_name: manticore_poc
    environment:
      # 禁用遥测数据收集
      - TELEMETRY=0
    ports:
      - "9306:9306"  # MySQL protocol (SphinxQL)
      - "9308:9308"  # HTTP JSON API
      - "9312:9312"  # Binary API (可选)
    volumes:
      - ./manticore_data:/var/lib/manticore
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9308/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
