#!/usr/bin/env python3
"""
官方异步客户端测试

使用 manticoresearch-python-asyncio 包进行真正的异步操作测试
基于官方仓库的示例代码进行功能验证
"""

import asyncio
import json
import numpy as np
import manticoresearch
from manticoresearch.rest import ApiException
from pprint import pprint

async def test_official_async_client():
    """测试官方异步客户端"""
    print("🚀 测试官方异步客户端 (manticoresearch-python-asyncio)")
    
    # 配置客户端
    configuration = manticoresearch.Configuration(
        host="http://127.0.0.1:9308"
    )
    
    try:
        # 使用异步上下文管理器
        async with manticoresearch.ApiClient(configuration) as api_client:
            print("✅ 异步客户端创建成功")
            
            # 创建API实例
            indexApi = manticoresearch.IndexApi(api_client)
            searchApi = manticoresearch.SearchApi(api_client)
            utilsApi = manticoresearch.UtilsApi(api_client)
            
            # 1. 测试连接和版本
            print("\n1. 测试连接...")
            try:
                sql_response = await utilsApi.sql('SHOW VERSION')
                print(f"✅ 连接成功")
                print(f"   版本信息: {sql_response}")
            except Exception as e:
                print(f"❌ 连接失败: {e}")
                return
            
            # 2. 创建表
            print("\n2. 创建表...")
            try:
                await utilsApi.sql("DROP TABLE IF EXISTS async_products")
                create_sql = """CREATE TABLE async_products (
                    title text,
                    price float
                ) engine='columnar'"""
                await utilsApi.sql(create_sql)
                print("✅ 表创建成功")
                
                # 查看表结构
                desc_response = await utilsApi.sql('DESC async_products')
                print(f"   表结构: {desc_response}")
            except Exception as e:
                print(f"❌ 表创建失败: {e}")
            
            # 3. 插入文档 - 使用官方示例格式
            print("\n3. 插入文档...")
            try:
                # 基础文档插入
                newDoc = {"title": "Crossbody Bag with Tassel", "price": 19.85}
                await indexApi.insert({"table": "async_products", "doc": newDoc})
                print("✅ 第一个文档插入成功")

                # 第二个文档插入
                newDoc2 = {
                    "title": "Pet Hair Remover Glove",
                    "price": 7.99
                }
                await indexApi.insert({"table": "async_products", "doc": newDoc2})
                print("✅ 第二个文档插入成功")

                # 批量插入
                docs_to_insert = []
                for i in range(3):
                    doc = {
                        "title": f"Product {i+1}",
                        "price": 10.0 + i * 5
                    }
                    docs_to_insert.append(
                        indexApi.insert({"table": "async_products", "doc": doc})
                    )
                
                # 并发插入
                results = await asyncio.gather(*docs_to_insert, return_exceptions=True)
                success_count = sum(1 for r in results if not isinstance(r, Exception))
                print(f"✅ 批量插入完成: {success_count}/{len(docs_to_insert)} 成功")
                
            except Exception as e:
                print(f"❌ 文档插入失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 4. 搜索文档
            print("\n4. 搜索文档...")
            try:
                # 基础搜索
                search_response = await searchApi.search({
                    "table": "async_products",
                    "query": {"query_string": "@title bag"}
                })
                print("✅ 基础搜索成功")
                hits = search_response.hits.hits
                print(f"   找到 {len(hits)} 条结果")
                for hit in hits:
                    print(f"   - 标题: {hit.source.get('title')}, 价格: {hit.source.get('price')}, 分数: {hit.score}")

                # 高亮搜索
                highlight_response = await searchApi.search({
                    "table": "async_products",
                    "query": {"query_string": "@title bag"},
                    "highlight": {"fields": {"title": {}}}
                })
                print("✅ 高亮搜索成功")
                hits = highlight_response.hits.hits
                for hit in hits:
                    if hit.highlight and hit.highlight.get('title'):
                        print(f"   高亮: {hit.highlight['title'][0]}")
                    else:
                        print(f"   - 标题: {hit.source.get('title')}, 分数: {hit.score}")
                
            except Exception as e:
                print(f"❌ 搜索失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 5. 全文搜索测试
            print("\n5. 全文搜索测试...")
            try:
                # 搜索所有文档
                all_docs_response = await searchApi.search({
                    "table": "async_products",
                    "query": {"match_all": {}}
                })
                print("✅ 全文搜索成功")
                hits = all_docs_response.hits.hits
                print(f"   找到 {len(hits)} 条全部结果")
                for hit in hits:
                    print(f"   - 标题: {hit.source.get('title')}, 价格: {hit.source.get('price')}, 分数: {hit.score}")

            except Exception as e:
                print(f"❌ 全文搜索失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 6. 并发搜索性能测试
            print("\n6. 并发搜索性能测试...")
            try:
                import time
                start_time = time.time()
                
                # 创建多个并发搜索任务
                search_tasks = []
                queries = ["bag", "glove", "product", "hair", "crossbody"]
                
                for i in range(20):  # 20个并发搜索
                    query = queries[i % len(queries)]
                    task = searchApi.search({
                        "table": "async_products",
                        "query": {"query_string": f"@title {query}"}
                    })
                    search_tasks.append(task)
                
                # 执行所有搜索
                search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
                end_time = time.time()
                
                # 统计结果
                successful_searches = [r for r in search_results if not isinstance(r, Exception)]
                failed_searches = [r for r in search_results if isinstance(r, Exception)]
                total_hits = sum(len(r.hits.hits) for r in successful_searches)
                
                print(f"✅ 并发搜索完成:")
                print(f"   总搜索次数: {len(search_tasks)}")
                print(f"   成功次数: {len(successful_searches)}")
                print(f"   失败次数: {len(failed_searches)}")
                print(f"   总结果数: {total_hits}")
                print(f"   总耗时: {(end_time - start_time)*1000:.2f}ms")
                if len(successful_searches) > 0:
                    print(f"   平均响应时间: {(end_time - start_time)*1000/len(successful_searches):.2f}ms")
                else:
                    print("   平均响应时间: N/A (没有成功的搜索)")
                
                if failed_searches:
                    print(f"   失败示例: {failed_searches[0]}")
                
            except Exception as e:
                print(f"❌ 并发测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 7. 查看最终状态
            print("\n7. 查看表状态...")
            try:
                count_response = await utilsApi.sql("SELECT COUNT(*) FROM async_products")
                print(f"✅ 表状态查询成功: {count_response}")
            except Exception as e:
                print(f"❌ 状态查询失败: {e}")
            
            print("\n🎉 官方异步客户端测试完成！")
            
    except Exception as e:
        print(f"❌ 客户端异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_official_async_client())
