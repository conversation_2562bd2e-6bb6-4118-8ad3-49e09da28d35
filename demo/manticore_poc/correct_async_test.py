#!/usr/bin/env python3
"""
正确的异步Manticore客户端测试

基于Context7文档，使用官方manticoresearch包的原生异步支持
"""

import asyncio
import json
import numpy as np
import manticoresearch

async def test_native_async_client():
    """测试原生异步客户端"""
    print("🚀 测试原生异步Manticore客户端")
    
    # 配置客户端
    config = manticoresearch.Configuration(
        host="http://127.0.0.1:9308"
    )
    
    # 使用异步上下文管理器
    async with manticoresearch.ApiClient(config) as client:
        print("✅ 异步客户端创建成功")
        
        # 创建API实例
        indexApi = manticoresearch.IndexApi(client)
        searchApi = manticoresearch.SearchApi(client)
        utilsApi = manticoresearch.UtilsApi(client)
        
        try:
            # 1. 测试连接
            print("\n1. 测试连接...")
            result = await utilsApi.sql("SHOW VERSION")
            print(f"✅ 连接成功: {result}")
            
            # 2. 创建表
            print("\n2. 创建表...")
            await utilsApi.sql("DROP TABLE IF EXISTS native_async_test")
            create_sql = """CREATE TABLE native_async_test (
                id bigint,
                content text,
                embedding float_vector(384)
            ) engine='columnar'"""
            await utilsApi.sql(create_sql)
            print("✅ 表创建成功")
            
            # 3. 插入文档
            print("\n3. 插入文档...")
            embedding = np.random.normal(0, 1, 384).tolist()
            
            insert_request = {
                "index": "native_async_test",
                "id": 1,
                "doc": {
                    "content": "人工智能是计算机科学的重要分支",
                    "embedding": embedding
                }
            }
            
            result = await indexApi.insert(insert_request)
            print(f"✅ 文档插入成功: {result}")
            
            # 4. 并发插入多个文档
            print("\n4. 并发插入多个文档...")
            tasks = []
            for i in range(5):
                embedding = np.random.normal(0, 1, 384).tolist()
                insert_req = {
                    "index": "native_async_test",
                    "id": i + 10,
                    "doc": {
                        "content": f"这是第 {i+1} 个测试文档，包含机器学习相关内容",
                        "embedding": embedding
                    }
                }
                task = indexApi.insert(insert_req)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            print(f"✅ 并发插入完成: {success_count}/{len(tasks)} 成功")
            
            # 5. 搜索文档
            print("\n5. 搜索文档...")
            search_request = {
                "index": "native_async_test",
                "query": {
                    "match": {
                        "content": "人工智能"
                    }
                }
            }
            
            result = await searchApi.search(search_request)
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 搜索成功，找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score}")
                print(f"     内容: {source.get('content', '')[:50]}...")
            
            # 6. 向量搜索
            print("\n6. 向量搜索...")
            query_vector = np.random.normal(0, 1, 384).tolist()
            
            vector_search_request = {
                "index": "native_async_test",
                "query": {
                    "knn": {
                        "field": "embedding",
                        "query_vector": query_vector,
                        "k": 5
                    }
                }
            }
            
            result = await searchApi.search(vector_search_request)
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 向量搜索成功，找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score:.4f}")
            
            # 7. 批量操作
            print("\n7. 批量操作...")
            docs = []
            for i in range(3):
                embedding = np.random.normal(0, 1, 384).tolist()
                docs.append({
                    "insert": {
                        "index": "native_async_test",
                        "id": i + 100,
                        "doc": {
                            "content": f"批量插入的深度学习文档 {i+1}",
                            "embedding": embedding
                        }
                    }
                })
            
            bulk_data = '\n'.join(json.dumps(doc) for doc in docs)
            result = await indexApi.bulk(bulk_data)
            print(f"✅ 批量操作成功: {result}")
            
            # 8. 高亮搜索
            print("\n8. 高亮搜索...")
            highlight_request = {
                "index": "native_async_test",
                "query": {
                    "match": {
                        "content": "机器学习"
                    }
                },
                "highlight": {
                    "fields": {
                        "content": {}
                    }
                }
            }
            
            result = await searchApi.search(highlight_request)
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 高亮搜索成功，找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get('_source', {})
                highlight = hit.get('highlight', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score}")
                if highlight.get('content'):
                    print(f"     高亮: {highlight['content'][0]}")
            
            # 9. 更新文档
            print("\n9. 更新文档...")
            update_request = {
                "index": "native_async_test",
                "id": 1,
                "doc": {
                    "content": "人工智能是计算机科学的重要分支，包括机器学习和深度学习"
                }
            }
            
            result = await indexApi.update(update_request)
            print(f"✅ 文档更新成功: {result}")
            
            # 10. 查看最终状态
            print("\n10. 查看表状态...")
            result = await utilsApi.sql("SELECT COUNT(*) FROM native_async_test")
            print(f"✅ 表状态: {result}")
            
            # 11. 性能测试 - 并发搜索
            print("\n11. 并发搜索性能测试...")
            import time
            start_time = time.time()
            
            search_tasks = []
            queries = ["人工智能", "机器学习", "深度学习", "文档", "测试"]
            
            for i in range(20):  # 20个并发搜索
                query = queries[i % len(queries)]
                search_req = {
                    "index": "native_async_test",
                    "query": {
                        "match": {
                            "content": query
                        }
                    }
                }
                task = searchApi.search(search_req)
                search_tasks.append(task)
            
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
            end_time = time.time()
            
            successful_searches = [r for r in search_results if not isinstance(r, Exception)]
            total_hits = sum(len(r.get('hits', {}).get('hits', [])) for r in successful_searches)
            
            print(f"✅ 并发搜索完成:")
            print(f"   搜索次数: {len(search_tasks)}")
            print(f"   成功次数: {len(successful_searches)}")
            print(f"   总结果数: {total_hits}")
            print(f"   耗时: {(end_time - start_time)*1000:.2f}ms")
            print(f"   平均响应时间: {(end_time - start_time)*1000/len(successful_searches):.2f}ms")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_native_async_client())
