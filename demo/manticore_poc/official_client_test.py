#!/usr/bin/env python3
"""
使用官方manticoresearch客户端测试

现在Python 3.11环境下测试官方客户端的异步功能
"""

import asyncio
import json
import numpy as np
import manticoresearch
from manticoresearch.rest import ApiException

async def test_official_client():
    """测试官方客户端"""
    print("🚀 测试官方 manticoresearch 客户端")
    
    # 配置客户端
    configuration = manticoresearch.Configuration(
        host="http://127.0.0.1:9308"
    )
    
    try:
        # 使用异步上下文管理器
        async with manticoresearch.ApiClient(configuration) as api_client:
            print("✅ 异步客户端创建成功")
            
            # 创建API实例
            utils_api = manticoresearch.UtilsApi(api_client)
            index_api = manticoresearch.IndexApi(api_client)
            search_api = manticoresearch.SearchApi(api_client)
            
            # 1. 测试连接
            print("\n1. 测试连接...")
            try:
                result = await utils_api.sql("SHOW VERSION")
                print(f"✅ 连接成功: {result}")
            except Exception as e:
                print(f"❌ 连接失败: {e}")
                return
            
            # 2. 创建表
            print("\n2. 创建表...")
            try:
                await utils_api.sql("DROP TABLE IF EXISTS official_test")
                create_sql = """CREATE TABLE official_test (
                    id bigint,
                    content text,
                    embedding float_vector(384)
                ) engine='columnar'"""
                await utils_api.sql(create_sql)
                print("✅ 表创建成功")
            except Exception as e:
                print(f"❌ 表创建失败: {e}")
            
            # 3. 插入文档
            print("\n3. 插入文档...")
            try:
                embedding = np.random.normal(0, 1, 384).tolist()
                
                doc = {
                    "id": 1,
                    "content": "人工智能是计算机科学的重要分支",
                    "embedding": embedding
                }
                
                insert_request = {
                    "index": "official_test",
                    "doc": doc
                }
                
                result = await index_api.insert(insert_request)
                print(f"✅ 文档插入成功: {result}")
            except Exception as e:
                print(f"❌ 文档插入失败: {e}")
            
            # 4. 搜索文档
            print("\n4. 搜索文档...")
            try:
                search_request = {
                    "index": "official_test",
                    "query": {
                        "match": {
                            "content": "人工智能"
                        }
                    }
                }
                
                result = await search_api.search(search_request)
                hits = result.get('hits', {}).get('hits', [])
                print(f"✅ 搜索成功，找到 {len(hits)} 条结果")
                
                for hit in hits:
                    source = hit.get('_source', {})
                    score = hit.get('_score', 0)
                    print(f"   - ID: {source.get('id')}, Score: {score}")
                    print(f"     内容: {source.get('content', '')}")
                    
            except Exception as e:
                print(f"❌ 搜索失败: {e}")
            
            # 5. 向量搜索
            print("\n5. 向量搜索...")
            try:
                query_vector = np.random.normal(0, 1, 384).tolist()
                
                vector_search = {
                    "index": "official_test",
                    "query": {
                        "knn": {
                            "field": "embedding",
                            "query_vector": query_vector,
                            "k": 5
                        }
                    }
                }
                
                result = await search_api.search(vector_search)
                hits = result.get('hits', {}).get('hits', [])
                print(f"✅ 向量搜索成功，找到 {len(hits)} 条结果")
                
                for hit in hits:
                    source = hit.get('_source', {})
                    score = hit.get('_score', 0)
                    print(f"   - ID: {source.get('id')}, Score: {score}")
                    
            except Exception as e:
                print(f"❌ 向量搜索失败: {e}")
            
            # 6. 批量操作
            print("\n6. 批量操作...")
            try:
                # 准备批量数据
                docs = []
                for i in range(3):
                    embedding = np.random.normal(0, 1, 384).tolist()
                    docs.append({
                        "insert": {
                            "index": "official_test",
                            "id": i + 10,
                            "doc": {
                                "content": f"这是第 {i+1} 个测试文档",
                                "embedding": embedding
                            }
                        }
                    })
                
                # 批量插入
                bulk_data = '\n'.join(json.dumps(doc) for doc in docs)
                result = await index_api.bulk(bulk_data)
                print(f"✅ 批量插入成功: {result}")
                
            except Exception as e:
                print(f"❌ 批量操作失败: {e}")
            
            # 7. 查看表状态
            print("\n7. 查看表状态...")
            try:
                result = await utils_api.sql("SELECT COUNT(*) FROM official_test")
                print(f"✅ 表状态: {result}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
                
    except Exception as e:
        print(f"❌ 客户端异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_official_client())
