#!/usr/bin/env python3
"""
异步包装器测试

使用asyncio的线程池来包装同步的manticoresearch客户端，
实现真正的异步操作，避免阻塞事件循环。

根据DeepWiki的调研结果，manticoresearch-python 9.0.0版本不支持原生异步操作，
因此我们使用线程池包装器来实现异步功能。
"""

import asyncio
import json
import numpy as np
import manticoresearch
from concurrent.futures import ThreadPoolExecutor
from functools import partial

class AsyncManticoreWrapper:
    """异步Manticore客户端包装器"""
    
    def __init__(self, host="http://127.0.0.1:9308"):
        self.configuration = manticoresearch.Configuration(host=host)
        self.executor = ThreadPoolExecutor(max_workers=10)
        
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.executor.shutdown(wait=True)
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """在线程池中运行同步函数"""
        loop = asyncio.get_event_loop()
        if kwargs:
            func = partial(func, **kwargs)
        return await loop.run_in_executor(self.executor, func, *args)
    
    async def sql(self, query: str):
        """执行SQL查询"""
        def _sql():
            with manticoresearch.ApiClient(self.configuration) as api_client:
                utils_api = manticoresearch.UtilsApi(api_client)
                return utils_api.sql(query)
        
        return await self._run_in_executor(_sql)
    
    async def insert(self, index: str, doc: dict, doc_id: int = None):
        """插入文档"""
        def _insert():
            with manticoresearch.ApiClient(self.configuration) as api_client:
                index_api = manticoresearch.IndexApi(api_client)
                insert_request = {
                    "index": index,
                    "doc": doc
                }
                if doc_id is not None:
                    insert_request["id"] = doc_id
                return index_api.insert(insert_request)
        
        return await self._run_in_executor(_insert)
    
    async def search(self, index: str, query: dict):
        """搜索文档"""
        def _search():
            with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                search_request = {
                    "index": index,
                    "query": query
                }
                return search_api.search(search_request)
        
        return await self._run_in_executor(_search)
    
    async def bulk(self, bulk_data: str):
        """批量操作"""
        def _bulk():
            with manticoresearch.ApiClient(self.configuration) as api_client:
                index_api = manticoresearch.IndexApi(api_client)
                return index_api.bulk(bulk_data)
        
        return await self._run_in_executor(_bulk)

async def test_async_wrapper():
    """测试异步包装器"""
    print("🚀 测试异步Manticore包装器")
    
    async with AsyncManticoreWrapper() as client:
        try:
            # 1. 测试连接
            print("\n1. 测试连接...")
            result = await client.sql("SHOW VERSION")
            print(f"✅ 连接成功: {result}")
            
            # 2. 创建表
            print("\n2. 创建表...")
            await client.sql("DROP TABLE IF EXISTS async_test")
            create_sql = """CREATE TABLE async_test (
                id bigint,
                content text,
                embedding float_vector(384)
            ) engine='columnar'"""
            await client.sql(create_sql)
            print("✅ 表创建成功")
            
            # 3. 插入文档
            print("\n3. 插入文档...")
            embedding = np.random.normal(0, 1, 384).tolist()
            
            doc = {
                "content": "人工智能是计算机科学的重要分支",
                "embedding": embedding
            }
            
            result = await client.insert("async_test", doc, doc_id=1)
            print(f"✅ 文档插入成功: {result}")
            
            # 4. 并发插入多个文档
            print("\n4. 并发插入多个文档...")
            tasks = []
            for i in range(5):
                embedding = np.random.normal(0, 1, 384).tolist()
                doc = {
                    "content": f"这是第 {i+1} 个测试文档",
                    "embedding": embedding
                }
                task = client.insert("async_test", doc, doc_id=i+10)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            print(f"✅ 并发插入完成: {success_count}/{len(tasks)} 成功")
            
            # 5. 搜索文档
            print("\n5. 搜索文档...")
            search_query = {
                "match": {
                    "content": "人工智能"
                }
            }
            
            result = await client.search("async_test", search_query)
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 搜索成功，找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score}")
                print(f"     内容: {source.get('content', '')}")
            
            # 6. 向量搜索
            print("\n6. 向量搜索...")
            query_vector = np.random.normal(0, 1, 384).tolist()
            
            vector_query = {
                "knn": {
                    "field": "embedding",
                    "query_vector": query_vector,
                    "k": 5
                }
            }
            
            result = await client.search("async_test", vector_query)
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 向量搜索成功，找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score:.4f}")
            
            # 7. 批量操作
            print("\n7. 批量操作...")
            docs = []
            for i in range(3):
                embedding = np.random.normal(0, 1, 384).tolist()
                docs.append({
                    "insert": {
                        "index": "async_test",
                        "id": i + 100,
                        "doc": {
                            "content": f"批量插入文档 {i+1}",
                            "embedding": embedding
                        }
                    }
                })
            
            bulk_data = '\n'.join(json.dumps(doc) for doc in docs)
            result = await client.bulk(bulk_data)
            print(f"✅ 批量操作成功: {result}")
            
            # 8. 查看最终状态
            print("\n8. 查看表状态...")
            result = await client.sql("SELECT COUNT(*) FROM async_test")
            print(f"✅ 表状态: {result}")
            
            # 9. 性能测试 - 并发搜索
            print("\n9. 并发搜索性能测试...")
            import time
            start_time = time.time()
            
            search_tasks = []
            for i in range(10):
                query = {
                    "match": {
                        "content": "文档"
                    }
                }
                task = client.search("async_test", query)
                search_tasks.append(task)
            
            search_results = await asyncio.gather(*search_tasks)
            end_time = time.time()
            
            total_hits = sum(len(r.get('hits', {}).get('hits', [])) for r in search_results)
            print(f"✅ 并发搜索完成:")
            print(f"   搜索次数: {len(search_tasks)}")
            print(f"   总结果数: {total_hits}")
            print(f"   耗时: {(end_time - start_time)*1000:.2f}ms")
            print(f"   平均响应时间: {(end_time - start_time)*1000/len(search_tasks):.2f}ms")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_async_wrapper())
