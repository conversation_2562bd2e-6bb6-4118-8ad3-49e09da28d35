#!/usr/bin/env python3
"""
Manticore Search POC 主程序

根据 demo/manticore_poc/README.md 要求实现的 POC 验证脚本。
验证 Manticore Search 在项目中作为长期记忆与上下文检索层的可行性。

使用方法:
    python3 demo/manticore_poc/main.py

验证内容:
1. 连接 Manticore Search 服务
2. 创建 docs_chunks 索引
3. 插入示例文档块（含 embedding）
4. 执行关键词和向量混合检索
5. 验证幂等性操作
"""

import sys
import asyncio
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 加载环境变量
load_dotenv(Path(__file__).parent / '.env')

try:
    import manticoresearch
    from manticoresearch.rest import ApiException
    import numpy as np
    HAS_MANTICORE = True
except ImportError:
    HAS_MANTICORE = False
    print("❌ 缺少依赖，请安装: pip install manticoresearch numpy")

class ManticorePOC:
    """Manticore Search POC 验证类"""
    
    def __init__(self):
        self.host = os.getenv('MANTICORE_HOST', '127.0.0.1')
        self.http_port = int(os.getenv('MANTICORE_HTTP_PORT', '9308'))
        self.sql_port = int(os.getenv('MANTICORE_SQL_PORT', '9306'))
        self.embedding_dim = int(os.getenv('MANTICORE_EMBEDDING_DIM', '384'))
        
        # 配置 Manticore 客户端
        self.configuration = manticoresearch.Configuration(
            host=f"http://{self.host}:{self.http_port}"
        )
        
        print(f"🔧 Manticore POC 配置:")
        print(f"   Host: {self.host}")
        print(f"   HTTP Port: {self.http_port}")
        print(f"   SQL Port: {self.sql_port}")
        print(f"   Embedding Dim: {self.embedding_dim}")
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 使用同步客户端进行测试
            api_client = manticoresearch.ApiClient(self.configuration)
            utils_api = manticoresearch.UtilsApi(api_client)

            # 简单的HTTP请求测试
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(f"http://{self.host}:{self.http_port}/") as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 连接成功，Manticore 版本: {data.get('version', {}).get('number', 'Unknown')}")
                        return True
                    else:
                        print(f"❌ 连接失败，HTTP状态码: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def create_docs_chunks_table(self) -> bool:
        """创建 docs_chunks 索引"""
        try:
            import aiohttp

            # 删除已存在的表
            drop_sql = "DROP TABLE IF EXISTS docs_chunks"
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(
                        f"http://{self.host}:{self.http_port}/cli",
                        data=drop_sql,
                        headers={'Content-Type': 'text/plain'}
                    ) as response:
                        if response.status == 200:
                            print("🗑️  删除已存在的 docs_chunks 表")
                except:
                    pass

                # 创建新表
                create_sql = f"""CREATE TABLE docs_chunks (
                    id string,
                    doc_id string,
                    content text,
                    metadata json,
                    embedding float_vector({self.embedding_dim})
                ) engine='columnar'"""

                async with session.post(
                    f"http://{self.host}:{self.http_port}/cli",
                    data=create_sql,
                    headers={'Content-Type': 'text/plain'}
                ) as response:
                    if response.status == 200:
                        print(f"✅ 创建 docs_chunks 表成功 (embedding dim: {self.embedding_dim})")
                        return True
                    else:
                        error_text = await response.text()
                        print(f"❌ 创建表失败，HTTP状态码: {response.status}, 错误: {error_text}")
                        return False

        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def generate_sample_embedding(self, text: str) -> List[float]:
        """生成示例向量（实际项目中应该调用 Embedding Service）"""
        # 简单的文本哈希向量生成（仅用于演示）
        np.random.seed(hash(text) % 2**32)
        return np.random.normal(0, 1, self.embedding_dim).tolist()
    
    async def insert_sample_chunks(self) -> bool:
        """插入示例文档块"""
        try:
            import aiohttp

            # 示例文档块
            sample_chunks = [
                {
                    "id": "d1_0",
                    "doc_id": "doc_001",
                    "content": "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
                    "metadata": {"source": "AI基础教程", "chapter": 1, "page": 1}
                },
                {
                    "id": "d1_1",
                    "doc_id": "doc_001",
                    "content": "机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够自动学习和改进的技术。",
                    "metadata": {"source": "AI基础教程", "chapter": 1, "page": 2}
                },
                {
                    "id": "d2_0",
                    "doc_id": "doc_002",
                    "content": "深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。",
                    "metadata": {"source": "深度学习指南", "chapter": 2, "page": 1}
                }
            ]

            async with aiohttp.ClientSession() as session:
                # 插入文档块
                for chunk in sample_chunks:
                    # 生成向量
                    embedding = self.generate_sample_embedding(chunk["content"])

                    # 准备文档
                    doc = {
                        "id": chunk["id"],
                        "doc_id": chunk["doc_id"],
                        "content": chunk["content"],
                        "metadata": chunk["metadata"],
                        "embedding": embedding
                    }

                    # 使用 insert 插入文档
                    async with session.post(
                        f"http://{self.host}:{self.http_port}/insert",
                        json={
                            "index": "docs_chunks",
                            "doc": doc
                        }
                    ) as response:
                        if response.status == 200:
                            print(f"✅ 插入文档块: {chunk['id']}")
                        else:
                            error_text = await response.text()
                            print(f"⚠️  插入文档块 {chunk['id']} 失败: {error_text}")

                print(f"✅ 完成插入 {len(sample_chunks)} 个文档块")
                return True

        except Exception as e:
            print(f"❌ 插入文档块失败: {e}")
            return False
    
    async def test_keyword_search(self) -> bool:
        """测试关键词搜索"""
        try:
            import aiohttp

            # 关键词搜索
            search_queries = ["人工智能", "机器学习", "深度学习", "神经网络"]

            async with aiohttp.ClientSession() as session:
                for query in search_queries:
                    search_request = {
                        "index": "docs_chunks",
                        "query": {
                            "match": {
                                "content": query
                            }
                        },
                        "limit": 5
                    }

                    async with session.post(
                        f"http://{self.host}:{self.http_port}/search",
                        json=search_request
                    ) as response:
                        if response.status == 200:
                            results = await response.json()
                            hits = results.get('hits', {}).get('hits', [])

                            print(f"🔍 关键词搜索 '{query}': {len(hits)} 条结果")
                            for hit in hits[:2]:  # 只显示前2条
                                source = hit.get('_source', {})
                                score = hit.get('_score', 0)
                                content_preview = source.get('content', '')[:50] + '...'
                                print(f"   - ID: {source.get('id')}, Score: {score:.3f}")
                                print(f"     内容: {content_preview}")
                        else:
                            error_text = await response.text()
                            print(f"⚠️  搜索 '{query}' 失败: {error_text}")

                return True

        except Exception as e:
            print(f"❌ 关键词搜索失败: {e}")
            return False
    
    async def test_vector_search(self) -> bool:
        """测试向量搜索"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                
                # 生成查询向量（模拟用户查询的向量）
                query_text = "什么是人工智能"
                query_vector = self.generate_sample_embedding(query_text)
                
                # 向量搜索
                vector_search_request = {
                    "index": "docs_chunks",
                    "query": {
                        "knn": {
                            "field": "embedding",
                            "query_vector": query_vector,
                            "k": 3
                        }
                    }
                }
                
                results = await search_api.search(vector_search_request)
                hits = results.get('hits', {}).get('hits', [])
                
                print(f"🎯 向量搜索 '{query_text}': {len(hits)} 条结果")
                for hit in hits:
                    source = hit.get('_source', {})
                    score = hit.get('_score', 0)
                    content_preview = source.get('content', '')[:50] + '...'
                    print(f"   - ID: {source.get('id')}, Score: {score:.3f}")
                    print(f"     内容: {content_preview}")
                
                return True
                
        except Exception as e:
            print(f"❌ 向量搜索失败: {e}")
            return False
    
    async def test_idempotency(self) -> bool:
        """测试幂等性"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                index_api = manticoresearch.IndexApi(api_client)
                search_api = manticoresearch.SearchApi(api_client)
                
                # 重复插入同一个文档
                test_doc = {
                    "id": "idempotency_test",
                    "doc_id": "test_doc",
                    "content": "这是一个幂等性测试文档",
                    "metadata": {"test": True},
                    "embedding": self.generate_sample_embedding("幂等性测试")
                }
                
                # 第一次插入
                await index_api.replace({
                    "index": "docs_chunks",
                    "doc": test_doc
                })
                
                # 第二次插入（应该替换而不是重复）
                await index_api.replace({
                    "index": "docs_chunks", 
                    "doc": test_doc
                })
                
                # 验证只有一条记录
                search_request = {
                    "index": "docs_chunks",
                    "query": {
                        "query_string": "@id idempotency_test"
                    }
                }
                
                results = await search_api.search(search_request)
                hits = results.get('hits', {}).get('hits', [])
                
                if len(hits) == 1:
                    print("✅ 幂等性测试通过：重复插入没有产生重复记录")
                    return True
                else:
                    print(f"❌ 幂等性测试失败：发现 {len(hits)} 条记录")
                    return False
                
        except Exception as e:
            print(f"❌ 幂等性测试失败: {e}")
            return False
    
    async def get_table_stats(self) -> Dict[str, Any]:
        """获取表统计信息"""
        try:
            async with manticoresearch.ApiClient(self.configuration) as api_client:
                utils_api = manticoresearch.UtilsApi(api_client)
                search_api = manticoresearch.SearchApi(api_client)
                
                # 获取表信息
                table_info = await utils_api.sql("SHOW TABLES")
                
                # 获取文档数量
                count_result = await search_api.search({
                    "index": "docs_chunks",
                    "query": {"match_all": {}},
                    "limit": 0
                })
                
                total_docs = count_result.get('hits', {}).get('total', {}).get('value', 0)
                
                return {
                    "table_exists": True,
                    "total_documents": total_docs,
                    "table_info": table_info
                }
                
        except Exception as e:
            return {
                "table_exists": False,
                "error": str(e)
            }

async def run_poc_validation():
    """运行 POC 验证"""
    print("🚀 开始 Manticore Search POC 验证")
    print("=" * 60)
    
    if not HAS_MANTICORE:
        print("❌ 缺少必要依赖，请先安装 requirements.txt")
        return False
    
    poc = ManticorePOC()
    
    # 验证清单
    validation_steps = [
        ("连接测试", poc.test_connection),
        ("创建索引", poc.create_docs_chunks_table),
        ("插入文档块", poc.insert_sample_chunks),
        ("关键词搜索", poc.test_keyword_search),
        ("向量搜索", poc.test_vector_search),
        ("幂等性测试", poc.test_idempotency)
    ]
    
    passed = 0
    total = len(validation_steps)
    
    for step_name, step_func in validation_steps:
        print(f"\n📋 执行: {step_name}")
        try:
            result = await step_func()
            if result:
                passed += 1
                print(f"✅ {step_name} - 通过")
            else:
                print(f"❌ {step_name} - 失败")
        except Exception as e:
            print(f"❌ {step_name} - 异常: {e}")
    
    # 获取最终统计
    print(f"\n📊 最终统计:")
    stats = await poc.get_table_stats()
    if stats.get('table_exists'):
        print(f"   总文档数: {stats.get('total_documents', 0)}")
    
    # 输出结果
    print("\n" + "=" * 60)
    print(f"📋 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 POC 验证成功！Manticore Search 集成可行")
        print("\n💡 验证要点:")
        print("   ✅ Manticore 服务正常运行")
        print("   ✅ 异步客户端连接成功")
        print("   ✅ 索引创建和文档插入正常")
        print("   ✅ 关键词和向量搜索功能正常")
        print("   ✅ 幂等性操作验证通过")
        return True
    else:
        print("⚠️  部分验证失败，请检查相关配置")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_poc_validation())
    sys.exit(0 if success else 1)
