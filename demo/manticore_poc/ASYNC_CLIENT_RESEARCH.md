# Manticore Search 异步客户端调研报告

## 调研背景

根据项目蓝图要求，需要使用 `asyncpg` 和 `manticore-async` 等异步驱动来保证I/O操作不阻塞FastAPI的事件循环。在实施过程中发现了异步客户端支持的复杂情况，需要进一步调研。

## 调研过程

### 1. 初始研究 (Context7 + DeepWiki)

**发现的信息：**
- Context7文档显示了大量异步代码示例，如：
  ```python
  async with manticoresearch.ApiClient(config) as client:
      indexApi = manticoresearch.IndexApi(client)
      searchApi = manticoresearch.SearchApi(client)
      utilsApi = manticoresearch.UtilsApi(client)
  ```
- DeepWiki提到了 `manticoresearch-python-asyncio` 客户端的存在
- 文档中显示了 `await` 语法的使用示例

**结论：** 初步认为官方客户端支持异步操作

### 2. 实际测试结果

**环境信息：**
- Python版本: 3.11.13
- manticoresearch包版本: 9.0.0
- Manticore Search服务版本: 6.3.6

**测试结果：**
```
TypeError: 'ApiClient' object does not support the asynchronous context manager protocol
```

**进一步调研发现：**
- manticoresearch-python 9.0.0版本**不支持原生异步操作**
- `ApiClient`类只实现了同步上下文管理器协议（`__enter__`和`__exit__`）
- 没有实现异步上下文管理器协议（`__aenter__`和`__aexit__`）

### 3. 深入调研结果

通过DeepWiki进一步查询得到明确答案：

> The `manticoresearch-python` library, specifically version 9.0.0, does not support asynchronous context managers or asynchronous operations directly through the `ApiClient` class.

**关键发现：**
1. 官方Python客户端是**纯同步**的
2. Context7文档中的异步示例可能来自：
   - 文档计划或草案
   - 其他版本的实现
   - 社区贡献的示例
3. `manticoresearch-python-asyncio` 包在PyPI上**不存在**

## 当前状况分析

### 可用的异步方案

1. **线程池包装器** (推荐)
   ```python
   import asyncio
   from concurrent.futures import ThreadPoolExecutor
   
   async def async_search():
       loop = asyncio.get_event_loop()
       with ThreadPoolExecutor() as executor:
           result = await loop.run_in_executor(executor, sync_search_function)
       return result
   ```

2. **HTTP API直接调用**
   ```python
   import aiohttp
   
   async def search_via_http():
       async with aiohttp.ClientSession() as session:
           async with session.post('http://localhost:9308/search', json=query) as response:
               return await response.json()
   ```

3. **第三方异步客户端** (需要进一步调研)

### 性能影响分析

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 线程池包装器 | 完整API支持，类型安全 | 线程开销，资源消耗 | 复杂查询，完整功能需求 |
| HTTP API | 真正异步，轻量级 | 手动序列化，错误处理复杂 | 简单查询，高并发场景 |
| 第三方客户端 | 可能的原生异步支持 | 维护风险，兼容性问题 | 需要评估具体实现 |

## 问题与疑问

### 1. 文档不一致性
- **问题：** Context7显示的异步示例与实际API不符
- **影响：** 可能误导开发者选择错误的技术方案
- **需要：** 确认这些示例的来源和适用版本

### 2. 蓝图要求vs实际情况
- **蓝图要求：** 使用 `manticore-async` 驱动
- **实际情况：** 该驱动可能不存在或名称不准确
- **需要：** 重新评估异步集成策略

### 3. 版本兼容性
- **问题：** 不同版本的manticoresearch可能有不同的异步支持
- **需要：** 调研其他版本或开发分支的异步支持情况

## 后续调研计划

### 1. 深入技术调研
- [ ] 检查manticoresearch-python的GitHub仓库，查看是否有异步支持的开发分支
- [ ] 调研Manticore Search的其他Python客户端实现
- [ ] 评估社区贡献的异步包装器

### 2. 性能基准测试
- [ ] 对比线程池包装器vs HTTP API的性能表现
- [ ] 测试不同并发级别下的资源消耗
- [ ] 评估在FastAPI中的实际表现

### 3. 替代方案评估
- [ ] 调研其他搜索引擎的异步Python客户端实现
- [ ] 评估是否需要自行开发异步客户端
- [ ] 考虑使用连接池优化同步客户端性能

## 临时解决方案

在进一步调研完成前，建议采用以下方案：

### 1. 线程池包装器实现
```python
class AsyncManticoreWrapper:
    def __init__(self, host="http://127.0.0.1:9308"):
        self.configuration = manticoresearch.Configuration(host=host)
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    async def search(self, index: str, query: dict):
        def _search():
            with manticoresearch.ApiClient(self.configuration) as api_client:
                search_api = manticoresearch.SearchApi(api_client)
                return search_api.search({"index": index, "query": query})
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _search)
```

### 2. 配置建议
- 合理设置线程池大小（建议10-20个线程）
- 实现连接池复用
- 添加适当的超时和重试机制
- 监控线程池使用情况

## 重大发现：官方异步客户端存在！

### 4. 最新发现 (2024-12-19 更新)

通过进一步的web搜索，发现了官方的异步客户端仓库：

**仓库地址：** https://github.com/manticoresoftware/manticoresearch-python-asyncio

**关键信息：**
- 这是一个**独立的异步客户端包**，不是主包的一部分
- 包名：`manticoresearch-python-asyncio`
- 支持真正的异步上下文管理器：`async with manticoresearch.ApiClient(configuration) as api_client:`
- 所有API调用都支持 `await` 语法
- 最新版本：1.0.0 (2025年4月1日发布)

**兼容性表：**
| 客户端版本 | Manticore Search | Python | 兼容性 |
|------------|------------------|--------|--------|
| 1.0.0+ | 9.2.14+ | 3.4+ | ✅ 完全兼容 |
| 1.0.0+ | 6.2.0-9.2.14 | 3.4+ | ⚠️ 部分兼容 |

**使用示例：**
```python
import manticoresearch
from manticoresearch.rest import ApiException

configuration = manticoresearch.Configuration(host="http://127.0.0.1:9308")

async with manticoresearch.ApiClient(configuration) as api_client:
    indexApi = manticoresearch.IndexApi(api_client)
    searchApi = manticoresearch.SearchApi(api_client)
    utilsApi = manticoresearch.UtilsApi(api_client)

    # 所有操作都支持 await
    await indexApi.insert({"index": "products", "doc": {"title": "test"}})
    result = await searchApi.search({"index": "products", "query": {"match_all": {}}})
```

## 结论 (更新)

**重要更正：** Manticore Search **确实有官方异步客户端支持**！

**正确的技术方案：**
1. **立即行动：** 安装并使用 `manticoresearch-python-asyncio` 包
2. **短期计划：** 验证异步客户端在当前项目中的集成效果
3. **长期策略：** 基于官方异步客户端构建POC架构

**对项目的影响：**
- ✅ 蓝图要求的异步驱动是可行的
- ✅ 可以实现真正的非阻塞I/O操作
- ✅ 符合FastAPI异步最佳实践
- ⚠️ 需要安装额外的包，不是主包的一部分

**下一步行动：**
1. 安装 `manticoresearch-python-asyncio` 包
2. 更新POC代码使用正确的异步客户端
3. 验证所有功能的异步支持
4. 更新项目依赖和文档

---

**文档创建时间：** 2024-12-19
**调研人员：** AI Assistant
**状态：** 已解决 - 找到官方异步客户端
**优先级：** 高 - 需要立即实施
