#!/usr/bin/env python3
"""
同步版本的Manticore Search测试脚本

使用requests库测试基本功能
"""

import requests
import json
import numpy as np

def test_manticore():
    """测试Manticore基本功能"""
    base_url = "http://127.0.0.1:9308"
    
    print("🚀 开始Manticore测试")
    
    # 1. 测试连接
    print("\n1. 测试连接...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 连接成功，版本: {data.get('version', {}).get('number', 'Unknown')}")
        else:
            print(f"❌ 连接失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return
    
    # 2. 创建表
    print("\n2. 创建表...")
    create_sql = """CREATE TABLE IF NOT EXISTS test_docs (
        id string,
        content text,
        embedding float_vector(384)
    ) engine='columnar'"""
    
    try:
        response = requests.post(
            f"{base_url}/cli",
            data=create_sql,
            headers={'Content-Type': 'text/plain'}
        )
        if response.status_code == 200:
            print("✅ 表创建成功")
            print(f"   响应: {response.text}")
        else:
            print(f"❌ 表创建失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 表创建异常: {e}")
    
    # 3. 插入文档
    print("\n3. 插入文档...")
    
    # 生成示例向量
    embedding = np.random.normal(0, 1, 384).tolist()
    
    doc = {
        "index": "test_docs",
        "id": 1,
        "doc": {
            "content": "人工智能是计算机科学的重要分支",
            "embedding": embedding
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/insert",
            json=doc,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文档插入成功: {result}")
        else:
            print(f"❌ 文档插入失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 文档插入异常: {e}")
    
    # 4. 搜索文档
    print("\n4. 搜索文档...")
    search_query = {
        "index": "test_docs",
        "query": {
            "match": {
                "content": "人工智能"
            }
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/search",
            json=search_query,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            result = response.json()
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 搜索成功，找到 {len(hits)} 条结果")
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score}")
                print(f"     内容: {source.get('content', '')[:50]}...")
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
    
    # 5. 查看表状态
    print("\n5. 查看表状态...")
    try:
        response = requests.post(
            f"{base_url}/cli",
            data="SELECT COUNT(*) FROM test_docs",
            headers={'Content-Type': 'text/plain'}
        )
        if response.status_code == 200:
            print(f"✅ 表状态查询成功:")
            print(f"   {response.text}")
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    # 6. 测试向量搜索
    print("\n6. 测试向量搜索...")
    query_vector = np.random.normal(0, 1, 384).tolist()
    
    # 使用KNN搜索
    vector_search = {
        "index": "test_docs",
        "query": {
            "knn": {
                "field": "embedding",
                "query_vector": query_vector,
                "k": 5
            }
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/search",
            json=vector_search,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            result = response.json()
            hits = result.get('hits', {}).get('hits', [])
            print(f"✅ 向量搜索成功，找到 {len(hits)} 条结果")
            for hit in hits:
                source = hit.get('_source', {})
                score = hit.get('_score', 0)
                print(f"   - ID: {source.get('id')}, Score: {score}")
        else:
            print(f"❌ 向量搜索失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 向量搜索异常: {e}")

if __name__ == "__main__":
    test_manticore()
