# services/embedding - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 核心原则：遵循 PROJECT_BLUEPRINT 的“可插拔、异步优先、单人可维护”原则

Embedding Service 负责将 document service 输出的 chunk 转为向量表示（embedding），并提供批量、异步与实时的向量化接口。本 POC 验证本服务与 Manticore 索引写入流水线的对接能力。

---
## 目标与成功标准（与蓝图一致）

- 仅测试远端 API（OpenAI 或兼容 API）两种模式（remote 优先，local 作为可选），接口保持一致。
- 提供异步批量接口以支持高吞吐（Dramatiq 任务触发）。
- 成功将 embedding 发送至索引写入流（Dramatiq -> Manticore）。
- 成功验证向量维度一致性（PROJECT_BLUEPRINT 强制 EMBEDDING_DIM 全局统一）。

成功标准：
- 本地批量 1000 chunks 处理延迟在可接受范围内（以机器为准，POC 验证吞吐）。
- Embedding 维度与 Manticore 字段配置一致且检索召回正确率满足基本标准。

---
## 说明（快速测试模式）

本 POC 专注于快速验证 embedding 服务功能，仅使用远端 API 模式：
- **远端 API**：调用远端 embedding API（例如 OpenAI 或兼容的第三方服务）
- **快速启动**：无需下载模型，直接配置 API Key 即可测试
- **轻量依赖**：最小化依赖包，专注核心功能验证

---
## 技术细节（与蓝图对齐）

- 服务框架：FastAPI (async)
- 模型调用：
  - 远端 API: 兼容 OpenAI-like 的 embedding API
  - 配置: 通过 `.env` 中的 EMBEDDING_REMOTE_URL 配置
  - 推荐模型（text-embedding-3系列）：
    - `text-embedding-3-small` (1536维，高效，推荐)
    - `text-embedding-3-large` (3072维，高精度)
    - `text-embedding-ada-002` (1536维，兼容性好)
- 批量/异步：
  - Dramatiq 用于处理长批量任务，Redis 作为 broker（增强项）
  - 支持 batch_size、worker_count 配置
- 输出与约束：
  - EMBEDDING_DIM 在 .env 中配置（与 Manticore 对齐）
  - 结果通过 JSON 返回：{ "results": [{ "id": "...", "vector": [...] }, ...] }

---
## 最小化 demo 文件结构

- demo/embedding_poc/
  - README.md（本文件）
  - requirements.txt
  - main.py / test_poc.py（示例：构造 ids/texts -> 调用 embed 接口 -> 输出向量）
  - .env（API 配置）
  - docker-compose.yml（可选，与 manticore/demo 集成时使用）

## 快速开始（使用 UV）

### 1. 安装 UV（如果尚未安装）
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 创建并激活虚拟环境
```bash
cd demo/embedding_poc
uv venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
uv pip install -r requirements.txt
```

示例 requirements.txt（快速测试版）:
```
fastapi
uvicorn[standard]
aiohttp
dramatiq
redis
requests
numpy
pydantic
```

---
## POC 核心流程（伪代码）

1. 从 Document Service 或测试文件读取 chunks
2. 按 batch_size 批量发送到远端 API
3. 对输出进行必要的归一化（cosine 兼容）
4. 将 embedding 与 chunk id 发送到 Dramatiq 任务以写入 Manticore

远端调用示例（OpenAI-like）:
```python
import requests
resp = requests.post(EMBEDDING_REMOTE_URL, json={
    "model": model_name,
    "input": texts
}, headers={"Authorization": f"Bearer {API_KEY}"})
# 解析 resp.json()["data"][i]["embedding"]
```

---
## 集成点与配置（与蓝图一致）

- 上游：services/document（chunks）
- 下游：Dramatiq Worker -> 写入 Manticore（docs_chunks）
- 配置（.env）示例（必须配置远端时至少填入下列项）：
  - EMBEDDING_REMOTE_URL=https://api.openai.com/v1/embeddings
  - REMOTE_API_KEY=your_openai_api_key_here
  - EMBEDDING_MODEL_NAME=text-embedding-3-small  # 推荐：高效且成本低
  - EMBEDDING_BATCH_SIZE=64
  - EMBEDDING_WORKER_COUNT=2
  - EMBEDDING_DIM=1536  # text-embedding-3-small 的维度

---
## 验证清单（快速测试）

- [ ] 远端 API 能批量生成向量且多次运行结果一致
- [ ] Embedding 服务能通过 HTTP 接口接收 chunks 并返回 vectors
- [ ] Dramatiq worker 能消费 embedding 任务并调用 Manticore 写入
- [ ] Embedding 向量维度与 Manticore 字段一致并可用于向量检索

---
## 风险与注意事项（快速测试）

- 远端 API 限速或费用风险：测试阶段使用小批量并启用重试/退避策略
- API Key 安全：确保 .env 文件不被提交到版本控制
- 模型替换策略：为避免服务中断，提供“local -> remote”无缝切换机制
- 向量精度与归一化需文档化为系统约定

---
## 参考（项目内）

- demo/document_poc/README.md（document -> embedding 流程）
- docs/PROJECT_BLUEPRINT.md（整体技术栈与集成指导）