from __future__ import annotations
import os
import asyncio
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import numpy as np
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Remote API only for fast testing

from openai import AsyncOpenAI
from concurrent.futures import ThreadPoolExecutor
import dramatiq
from dramatiq.brokers.redis import RedisBroker
import redis

# Configuration from ENV
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME", "text-embedding-3-small")
EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "64"))
EMBEDDING_DIM = int(os.getenv("EMBEDDING_DIM", "1536"))
OPENAI_API_KEY = os.getenv("REMOTE_API_KEY", "")
OPENAI_BASE_URL = os.getenv("EMBEDDING_REMOTE_URL", "https://api.openai.com/v1")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Configuration validation
if not OPENAI_API_KEY:
    print("⚠️  Warning: REMOTE_API_KEY not set in environment variables")

print(f"🚀 Embedding service starting with model: {EMBEDDING_MODEL_NAME} ({EMBEDDING_DIM}d)")

# Initialize OpenAI client
openai_client = AsyncOpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_BASE_URL
)

# Thread executor for async operations
_executor = ThreadPoolExecutor(max_workers=2)

# Dramatiq broker setup
redis_broker = RedisBroker(url=REDIS_URL)
dramatiq.set_broker(redis_broker)

class EmbedRequest(BaseModel):
    ids: List[str]
    texts: List[str]

class EmbedItem(BaseModel):
    id: str
    vector: List[float]

class EmbedResponse(BaseModel):
    results: List[EmbedItem]

def normalize_vectors(vecs: np.ndarray) -> np.ndarray:
    # cosine-compatible normalization
    norms = np.linalg.norm(vecs, axis=1, keepdims=True)
    norms[norms == 0] = 1.0
    return vecs / norms

async def call_remote_embedding(texts: List[str]) -> List[List[float]]:
    """
    Call remote embedding API using OpenAI client.
    """
    try:
        response = await openai_client.embeddings.create(
            model=EMBEDDING_MODEL_NAME,
            input=texts
        )

        # Extract embeddings from response
        embeddings = [item.embedding for item in response.data]
        return embeddings
    except Exception as e:
        raise RuntimeError(f"remote embedding failed: {str(e)}")

@dramatiq.actor
def process_embedding_batch(chunk_ids: List[str], texts: List[str]):
    """
    Dramatiq actor for processing embedding batches asynchronously.
    This would typically send results to Manticore Search.
    """
    import asyncio

    async def _process():
        try:
            # Call remote embedding API
            embeddings = await call_remote_embedding(texts)

            # Here you would typically send to Manticore Search
            # For POC, we just log the results
            print(f"Processed {len(embeddings)} embeddings for chunks: {chunk_ids[:3]}...")

            # TODO: Send to Manticore Search
            # manticore_client.insert_embeddings(chunk_ids, embeddings)

            return {"status": "success", "processed": len(embeddings)}
        except Exception as e:
            print(f"Error processing embedding batch: {e}")
            return {"status": "error", "error": str(e)}

    # Run async function in sync context
    return asyncio.run(_process())



from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.remote_only = True
    yield
    # Shutdown (if needed)
    pass

app = FastAPI(title="embedding-poc", lifespan=lifespan)

@app.post("/embed", response_model=EmbedResponse)
async def embed(req: EmbedRequest):
    if len(req.ids) != len(req.texts):
        raise HTTPException(status_code=400, detail="ids and texts length mismatch")

    texts = req.texts
    # Call remote API directly (async)
    vecs = await call_remote_embedding(texts)

    # Validate dimension
    if len(vecs) > 0 and len(vecs[0]) != EMBEDDING_DIM:
        raise HTTPException(status_code=500, detail=f"embedding dimension mismatch: got {len(vecs[0])}, expected {EMBEDDING_DIM}")

    results = [EmbedItem(id=i, vector=v) for i, v in zip(req.ids, vecs)]
    return EmbedResponse(results=results)

@app.post("/embed/batch")
async def embed_batch_async(req: EmbedRequest):
    """
    Asynchronous batch embedding using Dramatiq.
    Returns immediately with task info.
    """
    if len(req.ids) != len(req.texts):
        raise HTTPException(status_code=400, detail="ids and texts length mismatch")

    # Send to Dramatiq for async processing
    task = process_embedding_batch.send(req.ids, req.texts)

    return {
        "status": "queued",
        "task_id": task.message_id,
        "batch_size": len(req.texts)
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "embedding-poc"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=int(os.getenv("PORT", "8000")), log_level="info")