# Master-Know POC Environment Setup Guide

This guide explains how to set up the development environment for Master-Know POC development.

## Quick Start

1. **Copy the environment template:**
   ```bash
   cd demo
   cp .env.template .env
   ```

2. **Edit the environment file:**
   ```bash
   # Edit .env with your preferred editor
   nano .env  # or vim .env, code .env, etc.
   ```

3. **Start the required services:**
   ```bash
   docker-compose -f docker-compose.demo.yml up -d
   ```

4. **Verify the setup:**
   ```bash
   python verify_env.py
   ```

## Environment Configuration

### Required Services

The POC environment requires the following services:

- **PostgreSQL 13+**: Primary database for metadata storage
- **Redis 7+**: Cache and message broker for Dramatiq
- **Manticore Search**: Full-text and vector search engine

### Key Configuration Sections

#### Database Settings
```bash
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=master_know_poc
POSTGRES_USER=master_know_user
POSTGRES_PASSWORD=master_know_pass_2024
```

#### Redis Settings
```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

#### Manticore Search Settings
```bash
MANTICORE_HOST=localhost
MANTICORE_HTTP_PORT=9308
MANTICORE_MYSQL_PORT=9306
```

#### Embedding Configuration
```bash
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIM=384
EMBEDDING_BATCH_SIZE=32
```

#### Service Ports
```bash
DOCUMENT_SERVICE_PORT=9003
EMBEDDING_SERVICE_PORT=9001
LLM_SERVICE_PORT=9006
GATEWAY_SERVICE_PORT=9000
TOPIC_SERVICE_PORT=9004
USER_SERVICE_PORT=9002
```

## Docker Services

### Starting Services
```bash
# Start all services
docker-compose -f docker-compose.demo.yml up -d

# Start specific service
docker-compose -f docker-compose.demo.yml up -d postgres

# View logs
docker-compose -f docker-compose.demo.yml logs -f
```

### Stopping Services
```bash
# Stop all services
docker-compose -f docker-compose.demo.yml down

# Stop and remove volumes (⚠️ This will delete all data)
docker-compose -f docker-compose.demo.yml down -v
```

### Service Health Checks

The Docker Compose configuration includes health checks for all services:

- **PostgreSQL**: `pg_isready` command
- **Redis**: `redis-cli ping` command  
- **Manticore**: MySQL protocol connection test

## Development Tools

### Database Administration
- **Adminer**: http://localhost:8080
  - Server: postgres
  - Username: master_know_user
  - Password: master_know_pass_2024
  - Database: master_know_poc

### Redis Administration
- **Redis Commander**: http://localhost:8081

### Manticore Search
- **HTTP API**: http://localhost:9308
- **MySQL Protocol**: localhost:9306

## Verification Script

The `verify_env.py` script checks:

1. ✅ Environment file existence
2. ✅ Required environment variables
3. ✅ Docker Compose configuration validity
4. ✅ Service connectivity

```bash
python verify_env.py
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `.env` if default ports are occupied
2. **Permission issues**: Ensure Docker has proper permissions
3. **Memory issues**: Manticore requires at least 1GB RAM

### Service-Specific Issues

#### PostgreSQL
```bash
# Check if PostgreSQL is running
docker-compose -f docker-compose.demo.yml ps postgres

# View PostgreSQL logs
docker-compose -f docker-compose.demo.yml logs postgres

# Connect to PostgreSQL
docker-compose -f docker-compose.demo.yml exec postgres psql -U master_know_user -d master_know_poc
```

#### Redis
```bash
# Check Redis status
docker-compose -f docker-compose.demo.yml exec redis redis-cli ping

# View Redis info
docker-compose -f docker-compose.demo.yml exec redis redis-cli info
```

#### Manticore
```bash
# Check Manticore status
curl http://localhost:9308/

# Connect via MySQL protocol
mysql -h localhost -P 9306
```

## Environment Variables Reference

See `.env.template` for a complete list of available environment variables with descriptions and default values.

## Next Steps

After setting up the environment:

1. Verify all services are running: `python verify_env.py`
2. Proceed to POC development following the task order in `DEMO_DEV_ORDER.md`
3. Start with "阶段0-B：验证engines/text_splitter可用性"

## Security Notes

- Change default passwords in production
- Use strong SECRET_KEY values
- Restrict network access in production environments
- Enable SSL/TLS for production deployments
