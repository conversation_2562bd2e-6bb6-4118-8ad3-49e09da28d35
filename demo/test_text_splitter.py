#!/usr/bin/env python3
"""
Text Splitter Engine 验证脚本

验证 engines/text_splitter 模块的功能完整性，确保可以在POC中正常使用。

使用方法:
    python3 demo/test_text_splitter.py

验证内容:
1. TextSplitterEngine 导入和初始化
2. token_based 和 character_based 策略测试
3. 分割结果验证（content, index, token_count等）
4. 配置选项测试
5. 错误处理测试
"""

import sys
import asyncio
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from engines.text_splitter.engine import TextSplitterEngine
    from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
    from engines.text_splitter.models import Document, TextChunk
    from engines.text_splitter.config import TextSplitterConfig
    print("✅ 成功导入 engines.text_splitter 模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

# 测试文本样本
SAMPLE_TEXT = """
人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。

机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够自动学习和改进的技术。深度学习则是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。

自然语言处理（Natural Language Processing，NLP）是人工智能和语言学领域的分支学科。在NLP中，计算机需要理解和处理人类的自然语言，包括语音识别、语言理解、语言生成等任务。

计算机视觉是另一个重要的AI领域，它致力于使计算机能够识别和理解图像和视频中的内容。这包括物体检测、图像分类、人脸识别等应用。
""".strip()

def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_chunks_info(chunks: List[TextChunk], strategy_name: str):
    """打印分块信息"""
    print(f"\n📊 {strategy_name} 分割结果:")
    print(f"   总块数: {len(chunks)}")
    
    for i, chunk in enumerate(chunks[:3]):  # 只显示前3个块
        print(f"\n   块 {i+1}:")
        print(f"     索引: {chunk.chunk_index}")
        print(f"     字符位置: {chunk.start_char}-{chunk.end_char}")
        print(f"     长度: {chunk.get_length()}")
        print(f"     Token数: {chunk.token_count}")
        print(f"     预览: {chunk.get_preview(80)}")
    
    if len(chunks) > 3:
        print(f"   ... 还有 {len(chunks) - 3} 个块")

def test_basic_import():
    """测试基础导入功能"""
    print_separator("测试1: 基础导入和初始化")
    
    try:
        # 测试默认配置初始化
        engine = TextSplitterEngine()
        print("✅ 默认配置初始化成功")
        
        # 测试自定义配置初始化
        config = TextSplitterConfig(
            default_max_tokens=500,
            default_max_chars=1000
        )
        engine_custom = TextSplitterEngine(config)
        print("✅ 自定义配置初始化成功")
        
        # 测试统计信息
        stats = engine.get_stats()
        print(f"✅ 引擎统计信息: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def test_token_based_strategy():
    """测试基于Token的分割策略"""
    print_separator("测试2: Token-based 分割策略")
    
    try:
        # 创建引擎和策略
        engine = TextSplitterEngine()
        strategy = TokenBasedStrategy(max_tokens=200, model_name="gpt-3.5-turbo")
        
        # 执行分割
        chunks = engine.split_text(SAMPLE_TEXT, strategy)
        
        # 验证结果
        assert len(chunks) > 0, "分割结果不能为空"
        assert all(isinstance(chunk, TextChunk) for chunk in chunks), "所有块必须是TextChunk类型"
        assert all(chunk.content.strip() for chunk in chunks), "所有块内容不能为空"
        assert all(chunk.token_count is not None for chunk in chunks), "所有块必须有token计数"
        
        print_chunks_info(chunks, "Token-based")
        print("✅ Token-based 策略测试通过")
        
        return chunks
    except Exception as e:
        print(f"❌ Token-based 策略测试失败: {e}")
        return None

def test_character_based_strategy():
    """测试基于字符的分割策略"""
    print_separator("测试3: Character-based 分割策略")
    
    try:
        # 创建引擎和策略
        engine = TextSplitterEngine()
        strategy = CharacterBasedStrategy(max_chars=300)
        
        # 执行分割
        chunks = engine.split_text(SAMPLE_TEXT, strategy)
        
        # 验证结果
        assert len(chunks) > 0, "分割结果不能为空"
        assert all(isinstance(chunk, TextChunk) for chunk in chunks), "所有块必须是TextChunk类型"
        assert all(chunk.content.strip() for chunk in chunks), "所有块内容不能为空"
        assert all(chunk.get_length() <= 350 for chunk in chunks), "块长度不应超过设定值太多"  # 允许一些误差
        
        print_chunks_info(chunks, "Character-based")
        print("✅ Character-based 策略测试通过")
        
        return chunks
    except Exception as e:
        print(f"❌ Character-based 策略测试失败: {e}")
        return None

def test_document_splitting():
    """测试文档分割功能"""
    print_separator("测试4: 文档分割功能")
    
    try:
        # 创建文档对象
        document = Document(
            title="AI技术介绍",
            content=SAMPLE_TEXT,
            file_type="txt",
            size=len(SAMPLE_TEXT.encode('utf-8'))
        )
        
        # 创建引擎
        engine = TextSplitterEngine()
        
        # 执行文档分割
        result = engine.split_document(document)
        
        # 验证结果
        assert result.document_id == document.id, "文档ID必须匹配"
        assert result.total_chunks == len(result.chunks), "总块数必须匹配实际块数"
        assert result.strategy_used is not None, "必须记录使用的策略"
        
        print(f"✅ 文档分割成功:")
        print(f"   文档ID: {result.document_id}")
        print(f"   使用策略: {result.strategy_used}")
        print(f"   总块数: {result.total_chunks}")
        print(f"   平均块大小: {result.get_average_chunk_size():.1f} 字符")
        
        # 显示统计信息
        stats = result.get_statistics()
        print(f"   统计信息: {stats}")
        
        return result
    except Exception as e:
        print(f"❌ 文档分割测试失败: {e}")
        return None

def test_batch_processing():
    """测试批量处理功能"""
    print_separator("测试5: 批量处理功能")
    
    try:
        # 创建多个文档
        documents = []
        for i in range(3):
            doc = Document(
                title=f"测试文档 {i+1}",
                content=SAMPLE_TEXT[:200 + i*100],  # 不同长度的内容
                file_type="txt",
                size=len(SAMPLE_TEXT[:200 + i*100].encode('utf-8'))
            )
            documents.append(doc)
        
        # 创建引擎
        engine = TextSplitterEngine()
        
        # 执行批量分割
        results = engine.batch_split(documents)
        
        # 验证结果
        assert len(results) == len(documents), "结果数量必须匹配文档数量"
        assert all(result.error is None for result in results), "不应该有错误"
        
        print(f"✅ 批量处理成功:")
        for i, result in enumerate(results):
            print(f"   文档 {i+1}: {result.total_chunks} 个块")
        
        return results
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return None

def test_error_handling():
    """测试错误处理"""
    print_separator("测试6: 错误处理")
    
    try:
        engine = TextSplitterEngine()
        
        # 测试空文本
        try:
            strategy = TokenBasedStrategy()
            chunks = engine.split_text("", strategy)
            print("⚠️  空文本处理: 返回空列表或抛出异常都是合理的")
        except Exception as e:
            print(f"⚠️  空文本处理抛出异常: {e}")
        
        # 测试无效文档
        try:
            invalid_doc = Document(
                title="",
                content="",
                file_type="txt",
                size=0
            )
            result = engine.split_document(invalid_doc)
            print("⚠️  无效文档处理: 可能返回空结果")
        except Exception as e:
            print(f"⚠️  无效文档处理抛出异常: {e}")
        
        print("✅ 错误处理测试完成")
        return True
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证 engines/text_splitter 模块")
    
    # 执行所有测试
    tests = [
        test_basic_import,
        test_token_based_strategy,
        test_character_based_strategy,
        test_document_splitting,
        test_batch_processing,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not None and result is not False:
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    # 输出总结
    print_separator("测试总结")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    
    if passed == total:
        print("🎉 所有测试通过！engines/text_splitter 模块可用性验证成功")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    sys.exit(main())
