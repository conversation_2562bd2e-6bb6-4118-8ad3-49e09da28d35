# =============================================================================
# Master-Know POC Environment Configuration Template
# =============================================================================
# 
# This template contains all environment variables needed for POC development.
# Copy this file to .env and modify the values according to your environment.
#
# Usage:
#   cp .env.template .env
#   # Edit .env with your actual values
#
# =============================================================================

# =============================================================================
# BASIC PROJECT SETTINGS
# =============================================================================

# Project identification
PROJECT_NAME=Master-Know-POC
STACK_NAME=master-know-poc
ENVIRONMENT=local

# Domain and frontend settings
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================

# PostgreSQL connection settings
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=master_know_poc
POSTGRES_USER=master_know_user
POSTGRES_PASSWORD=master_know_pass_2024

# Database connection pool settings
DATABASE_POOL_SIZE=10
DATABASE_POOL_TIMEOUT=30
DATABASE_ECHO=false

# Computed DATABASE_URL (used by services)
# Format: postgresql+asyncpg://user:password@host:port/database
DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_SERVER}:${POSTGRES_PORT}/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Computed REDIS_URL (used by Dramatiq and caching)
REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# MANTICORE SEARCH CONFIGURATION
# =============================================================================

# Manticore Search connection settings
MANTICORE_HOST=localhost
MANTICORE_MYSQL_PORT=9306
MANTICORE_HTTP_PORT=9308
MANTICORE_SPHINXAPI_PORT=9312

# Manticore connection URLs
MANTICORE_MYSQL_URL=mysql://${MANTICORE_HOST}:${MANTICORE_MYSQL_PORT}
MANTICORE_HTTP_URL=http://${MANTICORE_HOST}:${MANTICORE_HTTP_PORT}

# =============================================================================
# EMBEDDING CONFIGURATION
# =============================================================================

# Embedding model settings
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIM=384
EMBEDDING_BATCH_SIZE=32

# Embedding service settings
EMBEDDING_SERVICE_HOST=localhost
EMBEDDING_SERVICE_PORT=9001
EMBEDDING_SERVICE_URL=http://${EMBEDDING_SERVICE_HOST}:${EMBEDDING_SERVICE_PORT}

# OpenAI API settings (optional, for remote embedding)
OPENAI_API_KEY=
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# =============================================================================
# LLM INTEGRATION CONFIGURATION
# =============================================================================

# LLM service settings
LLM_SERVICE_HOST=localhost
LLM_SERVICE_PORT=9006
LLM_SERVICE_URL=http://${LLM_SERVICE_HOST}:${LLM_SERVICE_PORT}

# OpenAI API settings
OPENAI_API_KEY=
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7

# Local LLM settings (optional)
LOCAL_LLM_MODEL=
LOCAL_LLM_HOST=localhost
LOCAL_LLM_PORT=8000

# =============================================================================
# SERVICE PORTS CONFIGURATION
# =============================================================================

# Individual service ports
DOCUMENT_SERVICE_PORT=9003
EMBEDDING_SERVICE_PORT=9001
LLM_SERVICE_PORT=9006
GATEWAY_SERVICE_PORT=9000
TOPIC_SERVICE_PORT=9004
USER_SERVICE_PORT=9002
MANTICORE_SERVICE_PORT=9000

# Service URLs (for inter-service communication)
DOCUMENT_SERVICE_URL=http://localhost:${DOCUMENT_SERVICE_PORT}
EMBEDDING_SERVICE_URL=http://localhost:${EMBEDDING_SERVICE_PORT}
LLM_SERVICE_URL=http://localhost:${LLM_SERVICE_PORT}
GATEWAY_SERVICE_URL=http://localhost:${GATEWAY_SERVICE_PORT}
TOPIC_SERVICE_URL=http://localhost:${TOPIC_SERVICE_PORT}
USER_SERVICE_URL=http://localhost:${USER_SERVICE_PORT}
MANTICORE_SEARCH_URL=http://localhost:${MANTICORE_SERVICE_PORT}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT settings
SECRET_KEY=your-secret-key-change-in-production-please
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
JWT_REFRESH_EXPIRE_DAYS=30

# CORS settings
BACKEND_CORS_ORIGINS=http://localhost,http://localhost:5173,http://localhost:3000

# =============================================================================
# DOCUMENT PROCESSING CONFIGURATION
# =============================================================================

# File upload settings
UPLOAD_DIR=./storage/uploads
PROCESSED_DIR=./storage/processed
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=[".txt", ".md", ".pdf", ".docx"]

# Text splitting settings
MAX_CHUNK_SIZE=1000
OVERLAP_SIZE=100
MIN_CHUNK_SIZE=200
DOCUMENT_SPLIT_MAX_TOKENS=512

# =============================================================================
# ASYNC PROCESSING CONFIGURATION
# =============================================================================

# Dramatiq settings
DRAMATIQ_BROKER=redis
DRAMATIQ_REDIS_URL=${REDIS_URL}
DRAMATIQ_RESULT_BACKEND=redis

# Processing settings
ENABLE_ASYNC_PROCESSING=true
PROCESSING_TIMEOUT=300
MAX_CONCURRENT_JOBS=5

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================

# Cache settings
ENABLE_DOCUMENT_CACHE=true
DOCUMENT_CACHE_TTL=7200
ENABLE_EMBEDDING_CACHE=true
EMBEDDING_CACHE_TTL=86400

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Logging settings
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=true

# Sentry settings (optional)
SENTRY_DSN=

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug settings
DEBUG=false
RELOAD=true

# API settings
API_V1_STR=/api/v1
API_TITLE=Master-Know POC API
API_DESCRIPTION=Master-Know POC API for development and testing
API_VERSION=1.0.0

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP settings
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true
SMTP_SSL=false
EMAILS_FROM_EMAIL=
EMAILS_FROM_NAME=${PROJECT_NAME}

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker image settings
DOCKER_IMAGE_BACKEND=master-know-backend
DOCKER_IMAGE_FRONTEND=master-know-frontend

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Test settings
TEST_DATABASE_URL=postgresql+asyncpg://test_user:test_pass@localhost:5432/test_db
TEST_REDIS_URL=redis://localhost:6379/1
ENABLE_TEST_MODE=false

# =============================================================================
# END OF CONFIGURATION
# =============================================================================
