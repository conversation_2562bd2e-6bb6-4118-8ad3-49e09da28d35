# =============================================================================
# Master-Know POC Docker Compose Configuration
# =============================================================================
#
# This is a simplified Docker Compose configuration for POC development.
# It includes only the essential services needed for POC testing.
#
# Usage:
#   docker-compose -f docker-compose.demo.yml up -d
#
# =============================================================================

services:
  # =============================================================================
  # DATABASE SERVICES
  # =============================================================================
  
  postgres:
    image: postgres:13
    container_name: master-know-postgres-poc
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-master_know_poc}
      POSTGRES_USER: ${POSTGRES_USER:-master_know_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-master_know_pass_2024}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-master_know_user} -d ${POSTGRES_DB:-master_know_poc}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - poc-network

  # =============================================================================
  # CACHE AND MESSAGE BROKER
  # =============================================================================
  
  redis:
    image: redis:7-alpine
    container_name: master-know-redis-poc
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - poc-network

  # =============================================================================
  # SEARCH ENGINE
  # =============================================================================
  
  manticore:
    image: manticoresearch/manticore:latest
    container_name: master-know-manticore-poc
    restart: unless-stopped
    environment:
      - EXTRA=1
    volumes:
      - manticore_data:/var/lib/manticore
      - ../manticore/manticore.conf:/etc/manticoresearch/manticore.conf:ro
    ports:
      - "${MANTICORE_MYSQL_PORT:-9306}:9306"   # MySQL protocol
      - "${MANTICORE_HTTP_PORT:-9308}:9308"    # HTTP JSON API
      - "${MANTICORE_SPHINXAPI_PORT:-9312}:9312" # SphinxAPI
    healthcheck:
      test: ["CMD-SHELL", "mysql -h127.0.0.1 -P9306 -e 'SHOW TABLES' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - poc-network

  # =============================================================================
  # DEVELOPMENT TOOLS (Optional)
  # =============================================================================
  
  adminer:
    image: adminer:latest
    container_name: master-know-adminer-poc
    restart: unless-stopped
    environment:
      ADMINER_DESIGN: pepa-linha-dark
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - poc-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: master-know-redis-commander-poc
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - poc-network

# =============================================================================
# VOLUMES
# =============================================================================

volumes:
  postgres_data:
    driver: local
    name: master-know-postgres-poc-data
  redis_data:
    driver: local
    name: master-know-redis-poc-data
  manticore_data:
    driver: local
    name: master-know-manticore-poc-data

# =============================================================================
# NETWORKS
# =============================================================================

networks:
  poc-network:
    driver: bridge
    name: master-know-poc-network
