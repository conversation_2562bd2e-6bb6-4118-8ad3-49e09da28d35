import asyncio
import httpx
import json
from main import GenerateRequest, Message

async def test_health_endpoint():
    """测试健康检查接口"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/health")
            print(f"Health check: {response.status_code} - {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False

async def test_root_endpoint():
    """测试根路径接口"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/")
            print(f"Root endpoint: {response.status_code}")
            print(json.dumps(response.json(), indent=2))
            return response.status_code == 200
        except Exception as e:
            print(f"Root endpoint failed: {e}")
            return False

async def test_generate_simple():
    """测试简单生成接口"""
    request_data = {
        "conversation_id": "test-conv-001",
        "user_query": "Hello, how are you?",
        "history": [],
        "max_tokens": 100,
        "temperature": 0.2
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json=request_data,
                timeout=30.0
            )
            
            print(f"Simple generate: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result['text'][:100]}...")
                print(f"Usage: {result['usage']}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Simple generate failed: {e}")
            return False

async def test_generate_with_history():
    """测试带历史记录的生成"""
    request_data = {
        "conversation_id": "test-conv-002",
        "user_query": "What did I just ask you?",
        "history": [
            {"role": "user", "content": "My name is Alice"},
            {"role": "assistant", "content": "Nice to meet you, Alice! How can I help you today?"}
        ],
        "max_tokens": 150,
        "temperature": 0.3
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json=request_data,
                timeout=30.0
            )
            
            print(f"Generate with history: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {result['text']}")
                print(f"Metadata: {result['metadata']}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"Generate with history failed: {e}")
            return False

async def test_error_handling():
    """测试错误处理"""
    # 测试空请求
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json={}
            )
            
            print(f"Error handling test: {response.status_code}")
            print(f"Error response: {response.text}")
            return response.status_code == 422  # Validation error expected
            
        except Exception as e:
            print(f"Error handling test failed: {e}")
            return False

async def run_all_tests():
    """运行所有测试"""
    print("🚀 Starting LLM POC Tests...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("Simple Generate", test_generate_simple),
        ("Generate with History", test_generate_with_history),
        ("Error Handling", test_error_handling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"Result: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"Result: ❌ FAIL - {e}")
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    total_tests = len(tests)
    passed_tests = sum(1 for r in results.values() if r)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the logs above.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    print("LLM Integration POC - Test Suite")
    print("Make sure the server is running: python main.py")
    print("Or: uvicorn main:app --reload")
    print()
    
    # 运行测试
    asyncio.run(run_all_tests())