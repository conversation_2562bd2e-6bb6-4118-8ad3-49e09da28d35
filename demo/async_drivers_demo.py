#!/usr/bin/env python3
"""
异步数据库驱动演示脚本

根据蓝图要求，演示asyncpg和manticore-async的使用方法和集成方案。
展示如何在POC项目中使用这些异步驱动来保证I/O操作不阻塞FastAPI的事件循环。

技术栈:
- asyncpg: PostgreSQL异步驱动
- manticoresearch-python: Manticore Search官方Python客户端
- FastAPI: 异步Web框架

使用方法:
    python3 demo/async_drivers_demo.py
"""

import sys
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_code_example(title: str, code: str):
    """打印代码示例"""
    print(f"\n📝 {title}:")
    print("```python")
    print(code.strip())
    print("```")

async def demonstrate_asyncpg_usage():
    """演示asyncpg的使用方法"""
    print_separator("AsyncPG - PostgreSQL异步驱动")
    
    print("🎯 AsyncPG特点:")
    print("   - 纯Python实现的PostgreSQL异步驱动")
    print("   - 高性能，支持连接池")
    print("   - 原生支持PostgreSQL数据类型")
    print("   - 完全兼容asyncio")
    
    # 基础连接示例
    basic_connection = """
import asyncio
import asyncpg

async def connect_to_postgres():
    # 建立连接
    conn = await asyncpg.connect(
        user='postgres',
        password='password',
        database='master_know',
        host='localhost',
        port=5432
    )
    
    # 执行查询
    rows = await conn.fetch('SELECT * FROM documents LIMIT 10')
    for row in rows:
        print(f"ID: {row['id']}, Title: {row['title']}")
    
    # 关闭连接
    await conn.close()

# 运行
asyncio.run(connect_to_postgres())
"""
    print_code_example("基础连接和查询", basic_connection)
    
    # 连接池示例
    pool_example = """
import asyncpg

async def setup_database_pool():
    # 创建连接池
    pool = await asyncpg.create_pool(
        user='postgres',
        password='password',
        database='master_know',
        host='localhost',
        port=5432,
        min_size=5,
        max_size=20
    )
    
    # 使用连接池
    async with pool.acquire() as conn:
        # 插入文档
        await conn.execute('''
            INSERT INTO documents (title, content, created_at)
            VALUES ($1, $2, $3)
        ''', 'AI基础', '人工智能是...', datetime.now())
        
        # 查询文档
        rows = await conn.fetch('''
            SELECT id, title, content 
            FROM documents 
            WHERE title ILIKE $1
        ''', '%AI%')
        
        return rows
    
    # 关闭连接池
    await pool.close()
"""
    print_code_example("连接池使用", pool_example)
    
    # 事务示例
    transaction_example = """
async def document_with_chunks_transaction(pool):
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 插入文档
            doc_id = await conn.fetchval('''
                INSERT INTO documents (title, content)
                VALUES ($1, $2)
                RETURNING id
            ''', 'Machine Learning', '机器学习是...')
            
            # 插入文档块
            chunks = [
                {'content': '机器学习是人工智能的一个分支', 'index': 0},
                {'content': '它通过算法让计算机从数据中学习', 'index': 1}
            ]
            
            for chunk in chunks:
                await conn.execute('''
                    INSERT INTO document_chunks (document_id, content, chunk_index)
                    VALUES ($1, $2, $3)
                ''', doc_id, chunk['content'], chunk['index'])
            
            return doc_id
"""
    print_code_example("事务处理", transaction_example)

async def demonstrate_manticore_async():
    """演示Manticore异步客户端的使用"""
    print_separator("Manticore Search - 异步搜索引擎")
    
    print("🎯 Manticore Search特点:")
    print("   - 高性能全文搜索引擎")
    print("   - 支持向量搜索和混合搜索")
    print("   - 官方Python异步客户端")
    print("   - 兼容MySQL协议和HTTP API")
    
    # 基础连接示例
    basic_manticore = """
import manticoresearch
from manticoresearch.rest import ApiException

async def setup_manticore_client():
    # 配置连接
    configuration = manticoresearch.Configuration(
        host="http://localhost:9308"
    )
    
    # 创建API客户端
    async with manticoresearch.ApiClient(configuration) as api_client:
        # 创建索引API
        index_api = manticoresearch.IndexApi(api_client)
        search_api = manticoresearch.SearchApi(api_client)
        utils_api = manticoresearch.UtilsApi(api_client)
        
        return index_api, search_api, utils_api
"""
    print_code_example("Manticore客户端设置", basic_manticore)
    
    # 创建表示例
    create_table = """
async def create_search_table():
    async with manticoresearch.ApiClient(configuration) as api_client:
        utils_api = manticoresearch.UtilsApi(api_client)
        
        # 创建表
        sql = '''
        CREATE TABLE doc_chunks (
            id bigint,
            doc_id bigint,
            title text,
            content text,
            chunk_index int,
            embedding float_vector(1536)
        ) engine='columnar'
        '''
        
        try:
            await utils_api.sql(sql)
            print("表创建成功")
        except ApiException as e:
            print(f"创建表失败: {e}")
"""
    print_code_example("创建搜索表", create_table)
    
    # 文档索引示例
    indexing_example = """
async def index_documents():
    async with manticoresearch.ApiClient(configuration) as api_client:
        index_api = manticoresearch.IndexApi(api_client)
        
        # 单个文档索引
        document = {
            "id": 1,
            "doc_id": 100,
            "title": "人工智能基础",
            "content": "人工智能是计算机科学的一个分支...",
            "chunk_index": 0,
            "embedding": [0.1, 0.2, 0.3, ...]  # 1536维向量
        }
        
        await index_api.insert({
            "index": "doc_chunks",
            "doc": document
        })
        
        # 批量索引
        docs = []
        for i, chunk in enumerate(chunks):
            docs.append({
                "insert": {
                    "index": "doc_chunks",
                    "id": chunk['id'],
                    "doc": {
                        "doc_id": chunk['doc_id'],
                        "title": chunk['title'],
                        "content": chunk['content'],
                        "chunk_index": i,
                        "embedding": chunk['embedding']
                    }
                }
            })
        
        # 批量插入
        bulk_data = '\\n'.join(json.dumps(doc) for doc in docs)
        await index_api.bulk(bulk_data)
"""
    print_code_example("文档索引", indexing_example)
    
    # 搜索示例
    search_example = """
async def search_documents():
    async with manticoresearch.ApiClient(configuration) as api_client:
        search_api = manticoresearch.SearchApi(api_client)
        
        # 全文搜索
        search_request = {
            "index": "doc_chunks",
            "query": {
                "query_string": "@title|content 人工智能"
            },
            "limit": 10,
            "highlight": {
                "fields": {
                    "title": {},
                    "content": {}
                }
            }
        }
        
        results = await search_api.search(search_request)
        
        # 向量搜索
        vector_search = {
            "index": "doc_chunks",
            "query": {
                "knn": {
                    "field": "embedding",
                    "query_vector": [0.1, 0.2, 0.3, ...],  # 查询向量
                    "k": 5
                }
            }
        }
        
        vector_results = await search_api.search(vector_search)
        
        return results, vector_results
"""
    print_code_example("搜索操作", search_example)

async def demonstrate_fastapi_integration():
    """演示FastAPI集成"""
    print_separator("FastAPI集成 - 异步Web服务")
    
    print("🎯 FastAPI异步集成要点:")
    print("   - 使用异步数据库驱动避免阻塞事件循环")
    print("   - 在应用启动时初始化连接池")
    print("   - 使用依赖注入管理数据库连接")
    print("   - 实现优雅的错误处理和资源清理")
    
    # FastAPI应用结构
    fastapi_structure = """
from fastapi import FastAPI, Depends, HTTPException
import asyncpg
import manticoresearch

app = FastAPI()

# 全局变量存储连接池
pg_pool = None
manticore_config = None

@app.on_event("startup")
async def startup_event():
    global pg_pool, manticore_config
    
    # 初始化PostgreSQL连接池
    pg_pool = await asyncpg.create_pool(
        user='postgres',
        password='password',
        database='master_know',
        host='localhost',
        port=5432,
        min_size=5,
        max_size=20
    )
    
    # 初始化Manticore配置
    manticore_config = manticoresearch.Configuration(
        host="http://localhost:9308"
    )
    
    print("数据库连接池初始化完成")

@app.on_event("shutdown")
async def shutdown_event():
    global pg_pool
    if pg_pool:
        await pg_pool.close()
    print("数据库连接池已关闭")
"""
    print_code_example("FastAPI应用结构", fastapi_structure)
    
    # 依赖注入
    dependency_injection = """
from typing import AsyncGenerator

async def get_pg_connection():
    '''PostgreSQL连接依赖'''
    async with pg_pool.acquire() as conn:
        yield conn

async def get_manticore_client():
    '''Manticore客户端依赖'''
    async with manticoresearch.ApiClient(manticore_config) as client:
        yield client

# API端点示例
@app.post("/documents/")
async def create_document(
    title: str,
    content: str,
    pg_conn = Depends(get_pg_connection),
    manticore_client = Depends(get_manticore_client)
):
    try:
        # 1. 保存到PostgreSQL
        doc_id = await pg_conn.fetchval('''
            INSERT INTO documents (title, content, created_at)
            VALUES ($1, $2, $3)
            RETURNING id
        ''', title, content, datetime.now())
        
        # 2. 分割文本（使用text_splitter）
        from engines.text_splitter.engine import TextSplitterEngine
        splitter = TextSplitterEngine()
        chunks = splitter.split_text(content, strategy="token_based")
        
        # 3. 保存chunks到PostgreSQL
        for i, chunk in enumerate(chunks):
            await pg_conn.execute('''
                INSERT INTO document_chunks (document_id, content, chunk_index)
                VALUES ($1, $2, $3)
            ''', doc_id, chunk.content, i)
        
        # 4. 索引到Manticore
        index_api = manticoresearch.IndexApi(manticore_client)
        for i, chunk in enumerate(chunks):
            await index_api.insert({
                "index": "doc_chunks",
                "doc": {
                    "id": f"{doc_id}_{i}",
                    "doc_id": doc_id,
                    "title": title,
                    "content": chunk.content,
                    "chunk_index": i
                }
            })
        
        return {
            "document_id": doc_id,
            "chunks_count": len(chunks),
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
"""
    print_code_example("依赖注入和API端点", dependency_injection)
    
    # 搜索端点
    search_endpoint = """
@app.get("/search/")
async def search_documents(
    query: str,
    limit: int = 10,
    manticore_client = Depends(get_manticore_client),
    pg_conn = Depends(get_pg_connection)
):
    try:
        # 1. 在Manticore中搜索
        search_api = manticoresearch.SearchApi(manticore_client)
        search_results = await search_api.search({
            "index": "doc_chunks",
            "query": {
                "query_string": f"@title|content {query}"
            },
            "limit": limit,
            "highlight": {
                "fields": {"content": {}}
            }
        })
        
        # 2. 获取文档详细信息
        doc_ids = [hit['_source']['doc_id'] for hit in search_results['hits']['hits']]
        
        if doc_ids:
            documents = await pg_conn.fetch('''
                SELECT id, title, created_at
                FROM documents
                WHERE id = ANY($1)
            ''', doc_ids)
            
            # 3. 组合结果
            results = []
            for hit in search_results['hits']['hits']:
                doc_info = next(
                    (doc for doc in documents if doc['id'] == hit['_source']['doc_id']),
                    None
                )
                results.append({
                    "score": hit['_score'],
                    "document": doc_info,
                    "chunk": hit['_source'],
                    "highlight": hit.get('highlight', {})
                })
            
            return {
                "total": search_results['hits']['total']['value'],
                "results": results
            }
        
        return {"total": 0, "results": []}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
"""
    print_code_example("搜索API端点", search_endpoint)

async def demonstrate_performance_benefits():
    """演示异步驱动的性能优势"""
    print_separator("异步驱动性能优势")
    
    print("🚀 异步I/O的优势:")
    print("   - 非阻塞操作：I/O操作不会阻塞事件循环")
    print("   - 高并发处理：单线程处理大量并发请求")
    print("   - 资源效率：减少线程切换开销")
    print("   - 响应性能：提高API响应速度")
    
    # 性能对比示例
    performance_comparison = """
import time
import asyncio
import asyncpg

# 同步方式（阻塞）
def sync_database_operations():
    # 模拟同步数据库操作
    time.sleep(0.1)  # 模拟I/O延迟
    return "sync result"

# 异步方式（非阻塞）
async def async_database_operations():
    # 真实的异步数据库操作
    async with pg_pool.acquire() as conn:
        result = await conn.fetchval('SELECT COUNT(*) FROM documents')
        return result

# 并发性能测试
async def performance_test():
    start_time = time.time()
    
    # 并发执行100个异步操作
    tasks = [async_database_operations() for _ in range(100)]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    print(f"异步操作耗时: {end_time - start_time:.2f}秒")
    print(f"处理请求数: {len(results)}")
    print(f"平均响应时间: {(end_time - start_time) / len(results) * 1000:.2f}ms")

# 运行性能测试
asyncio.run(performance_test())
"""
    print_code_example("性能对比测试", performance_comparison)

async def main():
    """主演示函数"""
    print("🚀 异步数据库驱动集成演示")
    print("根据蓝图要求：全面采用异步驱动，保证I/O操作不阻塞FastAPI的事件循环")
    print("=" * 80)
    
    # 执行各种演示
    await demonstrate_asyncpg_usage()
    await demonstrate_manticore_async()
    await demonstrate_fastapi_integration()
    await demonstrate_performance_benefits()
    
    print("\n" + "=" * 80)
    print("✅ 演示完成！")
    print("\n💡 异步驱动集成要点:")
    print("   1. 使用asyncpg替代psycopg2，获得真正的异步PostgreSQL操作")
    print("   2. 使用manticoresearch-python官方客户端进行异步搜索")
    print("   3. 在FastAPI应用启动时初始化连接池")
    print("   4. 使用依赖注入管理数据库连接")
    print("   5. 实现优雅的错误处理和资源清理")
    
    print("\n🔧 POC开发建议:")
    print("   1. 优先使用连接池管理数据库连接")
    print("   2. 合理配置连接池大小（min_size=5, max_size=20）")
    print("   3. 使用事务确保数据一致性")
    print("   4. 实现适当的错误处理和重试机制")
    print("   5. 监控异步操作的性能指标")

if __name__ == "__main__":
    asyncio.run(main())
