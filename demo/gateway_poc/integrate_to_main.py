#!/usr/bin/env python3
"""
Gateway POC Integration Script
Integrates the Gateway POC into the main Master-Know system
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

class GatewayIntegrator:
    def __init__(self):
        self.poc_dir = Path(__file__).parent
        self.project_root = self.poc_dir.parent.parent
        self.services_dir = self.project_root / "services"
        self.gateway_service_dir = self.services_dir / "gateway"
        
    def print_step(self, step: str):
        print(f"\n🔧 {step}")
        print("-" * 50)
    
    def create_gateway_service(self):
        """Create the gateway service in services/gateway"""
        self.print_step("Creating Gateway Service Structure")
        
        # Create services/gateway directory
        self.gateway_service_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy core files from POC
        files_to_copy = [
            "main.py",
            "config.py", 
            "auth.py",
            "rate_limiter.py",
            "http_client.py",
            "requirements.txt",
            "Dockerfile",
            ".env.example"
        ]
        
        for file_name in files_to_copy:
            src = self.poc_dir / file_name
            dst = self.gateway_service_dir / file_name
            if src.exists():
                shutil.copy2(src, dst)
                print(f"✅ Copied {file_name}")
            else:
                print(f"⚠️  {file_name} not found")
    
    def update_main_docker_compose(self):
        """Update the main docker-compose.yml to include gateway service"""
        self.print_step("Updating Main Docker Compose")
        
        main_compose = self.project_root / "docker-compose.yml"
        
        if not main_compose.exists():
            print("⚠️  Main docker-compose.yml not found, creating basic structure")
            self.create_main_docker_compose()
        else:
            print("✅ Main docker-compose.yml exists, manual integration needed")
            print("   Please add the gateway service configuration manually")
    
    def create_main_docker_compose(self):
        """Create a basic docker-compose.yml for the main system"""
        compose_content = """version: '3.8'

services:
  # Gateway Service (BFF)
  gateway:
    build: ./services/gateway
    environment:
      - BFF_HOST=0.0.0.0
      - BFF_PORT=8000
      - AUTH_JWT_SECRET=${AUTH_JWT_SECRET:-dev-secret-key}
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://user:8000
      - DOCUMENT_SERVICE_URL=http://document:8000
      - TOPIC_SERVICE_URL=http://topic:8000
      - LLM_SERVICE_URL=http://llm:8000
      - CONVERSATION_SERVICE_URL=http://conversation:8000
      - EMBEDDING_SERVICE_URL=http://embedding:8000
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.gateway.rule=Host(`localhost`)"
      - "traefik.http.routers.gateway.entrypoints=web"
      - "traefik.http.services.gateway.loadbalancer.server.port=8000"
    networks:
      - traefik-public
      - internal
    depends_on:
      - redis

  # Traefik Edge Gateway
  traefik:
    image: traefik:v3.3
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - traefik-public

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    networks:
      - internal

  # Topic Service (already implemented)
  topic:
    build: ./services/topic
    environment:
      - DATABASE_URL=********************************************/master_know
      - REDIS_URL=redis://redis:6379/1
    networks:
      - internal
    depends_on:
      - postgres
      - redis

  # PostgreSQL Database
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=master_know
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - internal

networks:
  traefik-public:
    external: false
  internal:
    external: false

volumes:
  postgres_data:
"""
        
        main_compose = self.project_root / "docker-compose.yml"
        main_compose.write_text(compose_content)
        print(f"✅ Created {main_compose}")
    
    def update_gateway_config(self):
        """Update gateway configuration for main system integration"""
        self.print_step("Updating Gateway Configuration")
        
        config_file = self.gateway_service_dir / "config.py"
        if config_file.exists():
            # Update service URLs to match main system
            content = config_file.read_text()
            
            # Update default service URLs
            content = content.replace(
                'user_service_url: str = "http://user:8000"',
                'user_service_url: str = "http://user:8000"'
            )
            content = content.replace(
                'topic_service_url: str = "http://topic:8000"',
                'topic_service_url: str = "http://topic:8000"'
            )
            
            config_file.write_text(content)
            print("✅ Updated gateway configuration")
    
    def create_integration_tests(self):
        """Create integration tests for the gateway with main system"""
        self.print_step("Creating Integration Tests")
        
        test_content = '''#!/usr/bin/env python3
"""
Gateway Integration Tests
Tests the gateway service integration with the main Master-Know system
"""

import asyncio
import aiohttp
import pytest
from typing import Dict, Any

BASE_URL = "http://localhost"

class TestGatewayIntegration:
    
    async def test_health_check(self):
        """Test gateway health check"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                assert response.status == 200
                data = await response.json()
                assert data["status"] == "healthy"
    
    async def test_topic_service_integration(self):
        """Test gateway integration with topic service"""
        async with aiohttp.ClientSession() as session:
            # Test topic aggregation
            async with session.get(f"{BASE_URL}/api/v1/aggregate/topic/test-topic") as response:
                assert response.status in [200, 503]  # 503 if services not ready
                data = await response.json()
                assert "topic_id" in data
    
    async def test_authentication_flow(self):
        """Test complete authentication flow"""
        async with aiohttp.ClientSession() as session:
            # Login
            login_data = {"username": "demo", "password": "demo123"}
            async with session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
                assert response.status == 200
                data = await response.json()
                assert "access_token" in data
                
                # Test protected endpoint
                headers = {"Authorization": f"Bearer {data['access_token']}"}
                async with session.get(f"{BASE_URL}/api/v1/auth/me", headers=headers) as response:
                    assert response.status == 200

if __name__ == "__main__":
    # Run basic tests
    async def run_tests():
        test = TestGatewayIntegration()
        await test.test_health_check()
        await test.test_topic_service_integration()
        await test.test_authentication_flow()
        print("✅ All integration tests passed!")
    
    asyncio.run(run_tests())
'''
        
        test_file = self.gateway_service_dir / "test_integration.py"
        test_file.write_text(test_content)
        print(f"✅ Created {test_file}")
    
    def create_startup_script(self):
        """Create startup script for the integrated system"""
        self.print_step("Creating System Startup Script")
        
        startup_content = '''#!/usr/bin/env python3
"""
Master-Know System Startup Script
Starts the complete Master-Know system with Gateway
"""

import subprocess
import time
import sys
import os
from pathlib import Path

def start_system():
    """Start the complete Master-Know system"""
    project_root = Path(__file__).parent
    
    print("🚀 Starting Master-Know System with Gateway...")
    
    # Start services
    try:
        process = subprocess.Popen(
            ["docker-compose", "up", "--build"],
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ Services starting...")
        print("📊 Traefik Dashboard: http://localhost:8080")
        print("🌐 Gateway API: http://localhost")
        print("📝 API Documentation: http://localhost/docs")
        print("🔍 Topic Service: Available through gateway")
        
        # Show logs
        for line in iter(process.stdout.readline, ''):
            if line:
                print(line.rstrip())
            if process.poll() is not None:
                break
                
    except KeyboardInterrupt:
        print("\\n🛑 Stopping services...")
        subprocess.run(["docker-compose", "down"], cwd=project_root)

if __name__ == "__main__":
    start_system()
'''
        
        startup_file = self.project_root / "start_system.py"
        startup_file.write_text(startup_content)
        startup_file.chmod(0o755)
        print(f"✅ Created {startup_file}")
    
    def run_integration(self):
        """Run the complete integration process"""
        print("🔧 Gateway POC Integration to Main System")
        print("=" * 60)
        
        try:
            self.create_gateway_service()
            self.update_gateway_config()
            self.update_main_docker_compose()
            self.create_integration_tests()
            self.create_startup_script()
            
            print("\n🎉 Integration Complete!")
            print("=" * 60)
            print("✅ Gateway service created in services/gateway")
            print("✅ Main docker-compose.yml updated")
            print("✅ Integration tests created")
            print("✅ System startup script created")
            
            print("\n📋 Next Steps:")
            print("1. Review and customize services/gateway/config.py")
            print("2. Update environment variables in .env")
            print("3. Run: python start_system.py")
            print("4. Test: python services/gateway/test_integration.py")
            
            return True
            
        except Exception as e:
            print(f"❌ Integration failed: {str(e)}")
            return False

if __name__ == "__main__":
    integrator = GatewayIntegrator()
    success = integrator.run_integration()
    sys.exit(0 if success else 1)
