{"generated_at": "2025-08-14T12:11:07.290077Z", "summary": "Aggregated top-level directory scan (sampled, max 20 entries per directory).", "directories": {".": ["release-notes.md", "docker-compose.traefik.yml", "docker-compose.override.yml", "README.md", ".env", "docker-compose.yml", "shrimp-rules.md", ".env.example", "SECURITY.md", "demo/docker-compose.demo.yml", "demo/.env.template", "demo/DOCUMENTATION_OPTIMIZATION_REPORT.md", "demo/DEMO_DEV_ORDER.md", "demo/.env", "demo/ENV_SETUP.md", "demo/gateway_poc/README.md", "demo/topic_poc/README.md", "demo/embedding_poc/requirements.txt", "demo/embedding_poc/Dockerfile", "demo/embedding_poc/README.md"], "backend": ["backend/Dockerfile", "backend/pyproject.toml", "backend/SYSTEM_STATUS_REPORT.md", "backend/app/main.py", "backend/app/api/main.py", "backend/app/alembic/README", "backend/.pytest_cache/README.md", "backend/.venv/lib/python3.12/site-packages/idna-3.10.dist-info/LICENSE.md", "backend/.venv/lib/python3.12/site-packages/mypyc/README.md", "backend/.venv/lib/python3.12/site-packages/mypyc/irbuild/main.py", "backend/.venv/lib/python3.12/site-packages/mypyc/doc/cpython-timings.md", "backend/.venv/lib/python3.12/site-packages/mypyc/doc/future.md", "backend/.venv/lib/python3.12/site-packages/mypyc/doc/dev-intro.md", "backend/.venv/lib/python3.12/site-packages/mypyc/external/googletest/README.md", "backend/.venv/lib/python3.12/site-packages/dotenv/main.py", "backend/.venv/lib/python3.12/site-packages/typer/main.py", "backend/.venv/lib/python3.12/site-packages/httpcore-1.0.5.dist-info/licenses/LICENSE.md", "backend/.venv/lib/python3.12/site-packages/httpx-0.27.2.dist-info/licenses/LICENSE.md", "backend/.venv/lib/python3.12/site-packages/starlette-0.38.6.dist-info/licenses/LICENSE.md", "backend/.venv/lib/python3.12/site-packages/pre_commit/main.py"], "demo": ["demo/docker-compose.demo.yml", "demo/.env.template", "demo/DOCUMENTATION_OPTIMIZATION_REPORT.md", "demo/DEMO_DEV_ORDER.md", "demo/.env", "demo/ENV_SETUP.md", "demo/gateway_poc/README.md", "demo/topic_poc/README.md", "demo/embedding_poc/requirements.txt", "demo/embedding_poc/Dockerfile", "demo/embedding_poc/README.md", "demo/embedding_poc/.env", "demo/embedding_poc/docker-compose.yml", "demo/embedding_poc/main.py", "demo/embedding_poc/.venv/lib/python3.12/site-packages/idna-3.10.dist-info/LICENSE.md", "demo/embedding_poc/.venv/lib/python3.12/site-packages/dotenv/main.py", "demo/embedding_poc/.venv/lib/python3.12/site-packages/httpcore-1.0.9.dist-info/licenses/LICENSE.md", "demo/embedding_poc/.venv/lib/python3.12/site-packages/numpy/ma/README.rst", "demo/embedding_poc/.venv/lib/python3.12/site-packages/numpy/_core/tests/data/umath-validation-set-README.txt", "demo/embedding_poc/.venv/lib/python3.12/site-packages/numpy/random/LICENSE.md"], "docs": ["docs/README.md", "docs/architecture_review.md", "docs/PROJECT_BLUEPRINT.md", "docs/architecture_issues.md", "docs/1_Product/01_brief.md", "docs/1_Product/02_prd_mvp.md", "docs/2_Architecture/02_data_model_v1.md", "docs/2_Architecture/01_overview.md", "docs/2_Architecture/03_modular_refactor_plan.md", "docs/2_Architecture/04_implementation_guide.md", "docs/3_Engineering/04_integration_testing_guide.md", "docs/3_Engineering/01_development_guide.md", "docs/3_Engineering/02_deployment_guide.md", "docs/3_Engineering/03_config_management.md", "docs/archive-docs/ARCHITECTURE.md"], "engines": [], "frontend": ["frontend/Dockerfile", "frontend/Dockerfile.playwright", "frontend/README.md", "frontend/.env"], "services": [], "deployment": [], "scripts": []}, "notes": {"max_per_directory": 20, "regenerate_command": "python scripts/generate_shrimp_scan.py --output demo/shrimp_scan_summary.json"}}