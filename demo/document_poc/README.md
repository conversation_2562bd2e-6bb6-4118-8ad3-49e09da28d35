# services/document - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo  
**状态**: 草稿

---
## 说明（遵循 PROJECT_BLUEPRINT）

本 README 按照项目蓝图规定的技术栈与设计原则编写。Document Service 的核心是接收用户上传的文档、进行语义化分割（TextSplitter）、元数据管理，并将 chunk 流水线交由 Embedding Service 与 Manticore 进行后续处理。所有外部依赖的版本与接口应与蓝图保持一致。

---
## 核心目标

- 提供可靠的文档上传接口（支持 .txt, .md, .pdf；推荐使用PyMuPDF进行PDF处理）
- 支持大文件流式上传，避免内存峰值
- 将文档通过 engines/text_splitter 进行语义分割，生成 chunk
- 将 chunk 元数据存入 PostgreSQL（通过 SQLModel）
- 异步将 chunk 送入 Embedding Service（通过 Dramatiq 任务队列）
- 将 embedding 与 chunk 写入 Manticore（通过官方异步客户端 manticoresearch-python-asyncio）

---
## 技术细节（与蓝图对齐）

- Web 框架：FastAPI（异步，支持流式文件上传）
- 数据模型：SQLModel（与 Pydantic 2.5+ 组合）
- 异步驱动：asyncpg（PostgreSQL），manticoresearch-python-asyncio（搜索引擎）
- 文档处理：PyMuPDF（PDF解析，性能优秀），python-multipart（文件上传）
- 文本分割：engines/text_splitter（项目内实现）
- 后台任务：Dramatiq（Redis broker）
- 存储：PostgreSQL（文档元数据），Manticore（chunk 索引）
- 容器编排：Docker Compose，网关 Traefik（在总 compose 中配置）

---
## 最小化 demo 文件结构

- demo/document_poc/
  - README.md（本文件）
  - requirements.txt（示例依赖）
  - main.py（演示上传 -> split -> queue -> index 流程）
  - docker-compose.yml（若需本地 Manticore，可选）

示例 requirements.txt（示意）:
```
fastapi
uvicorn[standard]
sqlmodel
asyncpg
dramatiq
redis
requests
python-multipart  # 文件上传支持
pymupdf  # PDF处理，性能优秀
engines-text-splitter==0.1.0  # 如果从本仓库复用
git+https://github.com/manticoresoftware/manticoresearch-python-asyncio.git  # 异步客户端
```

---
## 核心 POC 流程（伪代码）

1. 接收用户上传或读取示例文档
2. 调用 engines/text_splitter.split_text -> 返回 chunks
3. 将 chunks 持久化到 PostgreSQL（Document / Chunk 模型）
4. 发布 Dramatiq 任务：embedding_and_index(chunk_id)
5. Worker 调用 Embedding Service 获得向量并写入 Manticore

伪代码示例：
```python
# main.py 流程示意
from fastapi import FastAPI, File, UploadFile
from engines.text_splitter import TextSplitterEngine
from backend.app.services.document.document_service import save_chunks, publish_embedding_task

app = FastAPI()
splitter = TextSplitterEngine({})

@app.post("/upload")
async def upload(file: UploadFile = File(...)):
    text = await file.read()
    chunks = splitter.split_text(text.decode(), strategy="token")
    chunk_ids = save_chunks(chunks)  # 写入 Postgres via SQLModel
    publish_embedding_task(chunk_ids)  # 使用 Dramatiq 发布任务
    return {"chunk_count": len(chunks)}
```

---
## 集成与配置（与蓝图一致）

- 配置项（示例 .env）：
  - DATABASE_URL=postgresql+asyncpg://user:pass@postgres:5432/db
  - REDIS_URL=redis://redis:6379/0
  - EMBEDDING_SERVICE_URL=http://embedding:8000/embed
  - MANTICORE_HOST=manticore
  - MANTICORE_HTTP_PORT=9308
  - DOCUMENT_SPLIT_MAX_TOKENS=512

- API 路由草案：
  - POST /api/v1/documents/upload -> 接受文件并返回 document_id, task_id
  - GET /api/v1/documents/{id}/status -> 返回处理/索引状态
  - GET /api/v1/documents/{id}/chunks -> 返回 chunk 列表与元数据

---
## 验证清单（与蓝图 P0/P1 对齐）

- [ ] 上传接口能成功接收文件并返回 task_id
- [ ] engines/text_splitter 在不同文档类型上分割出合理 chunk
- [ ] chunk 元数据成功写入 PostgreSQL（通过 SQLModel 验证）
- [ ] Dramatiq worker 能接收任务并调用 Embedding Service
- [ ] Embedding 与 chunk 能写入 Manticore 并可通过检索召回

---
## 风险与注意事项

- 确保 tokenization 与 embedding 的兼容性（分词/归一化策略）
- 对于大文件，使用分片与批量任务避免内存峰值
- 持久化与幂等性：任务应支持重试，不重复写入索引

---
## 参考（项目内）

- engines/text_splitter/（项目内实现）
- backend/app/services/document/（服务实现参考）
- docs/PROJECT_BLUEPRINT.md（总体蓝图与技术栈）
