# Manticore Search 模块变更日志

## [修复] 2025-08-18 - 配置兼容性问题解决

### 问题描述
Manticore 容器在最新版本中无法正常启动，出现配置文件解析错误和重启循环。

### 根本原因分析
经过详细调试发现，问题不在于 b59e190 提交的 Manticore 集成代码（代码本身完全正常），而是配置文件中包含了新版本 Manticore Search 13.6.7 不再支持的废弃配置项。

### 修复的配置问题

1. **`compat_sphinxql_magics` 配置项已废弃**
   - 错误: `ERROR: unknown key name 'compat_sphinxql_magics'`
   - 解决: 移除此配置项

2. **`data_dir` 与表声明冲突**
   - 错误: `'data_dir' cannot be mixed with table declarations`
   - 解决: 移除预定义表声明，使用动态表创建

3. **`charset_type` 配置项已移除**
   - 警告: `key 'charset_type' was permanently removed`
   - 解决: 删除此配置项

4. **binlog 路径问题**
   - 错误: `failed to open '/var/lib/manticore/binlog/binlog.lock'`
   - 解决: 禁用 binlog (`binlog_path =`)

### 最终配置
创建了最小化的 `manticore/manticore.conf`：

```conf
searchd
{
    data_dir = /var/lib/manticore
    listen = 9306:mysql41
    listen = 9312
    listen = 9308:http
    pid_file = /var/run/manticore/searchd.pid
    binlog_path =
}
```

### 验证结果
所有测试通过：
- ✅ 健康检查
- ✅ SQL 执行
- ✅ 表创建
- ✅ 文档插入
- ✅ 搜索功能

### 影响
- **代码**: 无需修改，b59e190 的集成代码完全可用
- **配置**: 简化为最小可用配置
- **功能**: 所有搜索功能正常工作
- **性能**: 无影响

### 文档更新
- 创建了完整的 README.md 文档
- 添加了 QUICKSTART.md 快速开始指南
- 更新了主项目 README.md

---

## [集成] 2025-08-16 - 初始 Manticore Search 集成

### 新增功能
- 异步 Manticore 客户端
- 搜索服务层
- API 路由集成
- 完整测试套件

### 技术实现
- 使用官方 `manticoresearch-asyncio-devel` 包
- 支持全文搜索和向量搜索
- 异步上下文管理器模式
- 完整的错误处理和日志记录

---

**维护者**: AI Assistant  
**最后更新**: 2025-08-18
