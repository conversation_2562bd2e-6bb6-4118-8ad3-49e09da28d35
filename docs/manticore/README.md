# Manticore Search 集成模块

## 概述

本项目已成功集成 Manticore Search 作为全文搜索和向量搜索引擎。Manticore 模块提供了完整的异步搜索功能，包括文档索引、全文搜索和向量相似度搜索。

## 模块状态

✅ **完全可用** - 所有核心功能已通过测试验证

### 已验证功能
- ✅ 异步客户端连接和健康检查
- ✅ SQL 查询执行
- ✅ 动态表创建和管理
- ✅ 文档插入和更新
- ✅ 全文搜索功能
- ✅ 搜索服务集成
- ✅ API 路由功能

## 架构组件

```
backend/app/
├── manticore_client.py           # 基础异步客户端
├── services/search/              # 搜索服务模块
│   ├── manticore_service.py      # 高级搜索服务
│   └── search_manager.py         # 搜索管理器
├── api/routes/search.py          # 搜索API路由
└── test_manticore.py             # 测试脚本
```

## 配置文件

### 当前配置 (`manticore/manticore.conf`)

使用最小化配置，已移除所有废弃的配置项：

```conf
searchd
{
    # 数据目录
    data_dir = /var/lib/manticore
    
    # 监听端口
    listen = 9306:mysql41  # MySQL协议
    listen = 9312          # SphinxAPI
    listen = 9308:http     # HTTP API
    
    # PID文件
    pid_file = /var/run/manticore/searchd.pid
    
    # 禁用binlog（简化配置）
    binlog_path =
}
```

### 已修复的配置问题

- ❌ `compat_sphinxql_magics` - 已废弃，已移除
- ❌ `charset_type` - 已永久移除，已删除
- ❌ 预定义表声明 - 与 `data_dir` 冲突，已移除
- ✅ 使用动态表创建替代静态配置

## 启动和测试

### 1. 启动服务

```bash
# 启动所有服务（包括 Manticore）
docker compose up -d

# 仅启动 Manticore
docker compose up -d manticore

# 检查服务状态
docker compose ps manticore
```

### 2. 运行测试

#### 连接测试
```bash
cd backend
source .venv/bin/activate
python app/test_manticore.py --connection-only
```

#### 完整功能测试
```bash
cd backend
source .venv/bin/activate
python app/test_manticore.py
```

#### 预期测试结果
```
Testing health check...
✅ Health check passed

Testing SQL execution...
✅ SQL execution passed

Testing table creation...
✅ Table creation passed

Testing document insertion...
✅ Document insertion passed

Testing document search...
✅ Search passed - found 1 results

Test Summary: 5/5 tests passed
🎉 All tests passed!
```

### 3. 手动验证

#### 检查 Manticore 状态
```bash
docker compose exec manticore searchd --status
```

#### 测试 HTTP API
```bash
# 健康检查
curl -s http://localhost:9308/

# 查看表列表
curl -s -X POST http://localhost:9308/sql \
  -H "Content-Type: application/json" \
  -d '{"query":"SHOW TABLES"}'
```

## API 端点

### 搜索 API
- `POST /api/v1/search/documents` - 通用搜索
- `POST /api/v1/search/documents/semantic` - 语义搜索  
- `GET /api/v1/search/documents` - 简单查询搜索
- `GET /api/v1/search/health` - 健康检查
- `GET /api/v1/search/stats` - 统计信息

## 依赖项

- `manticoresearch-asyncio-devel` - 官方异步客户端
- `numpy>=1.24.0` - 数值计算
- `aiohttp>=3.0.0` - HTTP客户端
- `pydantic>=2` - 数据验证

## 故障排除

### 常见问题

1. **容器重启循环**
   - 检查配置文件语法
   - 查看日志：`docker compose logs manticore`

2. **连接失败**
   - 确认端口映射：9306, 9308, 9312
   - 检查防火墙设置

3. **表创建失败**
   - 确认 `data_dir` 配置正确
   - 检查容器内目录权限

### 调试命令

```bash
# 查看详细日志
docker compose logs --tail=50 manticore

# 进入容器调试
docker compose exec manticore bash

# 检查配置文件
docker compose exec manticore cat /etc/manticoresearch/manticore.conf
```

## 使用示例

### 基础用法

```python
from app.manticore_client import ManticoreClient

# 创建客户端
client = ManticoreClient(host="localhost", port=9308)

# 使用异步上下文管理器
async with client:
    # 健康检查
    is_healthy = await client.health_check()

    # 创建表
    schema = {
        "title": "text",
        "content": "text",
        "category_id": "integer"
    }
    await client.create_table("documents", schema)

    # 插入文档
    document = {
        "title": "示例文档",
        "content": "这是一个测试文档",
        "category_id": 1
    }
    await client.insert_document("documents", document, doc_id=1)

    # 搜索
    results = await client.search("documents", "测试", limit=10)
```

### 高级搜索服务

```python
from app.services.search.manticore_service import ManticoreSearchService

service = ManticoreSearchService()

# 索引文档
await service.index_document(
    table_name="documents",
    doc_id=1,
    title="标题",
    content="内容",
    source="来源"
)

# 全文搜索
results = await service.search_documents(
    table_name="documents",
    query="搜索关键词",
    limit=10,
    filters={"category_id": 1}
)

# 向量搜索（如果有embedding）
vector_results = await service.vector_search(
    table_name="documents",
    query_vector=[0.1, 0.2, 0.3, ...],
    k=5
)
```

## 开发注意事项

1. **异步上下文管理器**：必须使用 `async with client:` 模式
2. **动态表创建**：使用 `CREATE TABLE` SQL 而非配置文件
3. **错误处理**：所有 API 调用都包含完整的异常处理
4. **连接池**：客户端自动管理连接池和重连
5. **表结构**：支持 text、integer、bigint、timestamp 等字段类型
6. **批量操作**：支持批量文档插入以提高性能

## 性能优化建议

- 使用批量插入处理大量文档
- 合理设置搜索结果限制（limit）
- 对频繁搜索的字段建立适当的索引
- 使用过滤器缩小搜索范围

## 版本兼容性

- ✅ Manticore Search 13.6.7+
- ✅ Python 3.8+
- ✅ Docker Compose 2.0+

---

**最后更新**: 2025-08-18
**状态**: 生产就绪 ✅
