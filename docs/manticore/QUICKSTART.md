# Manticore Search 快速开始指南

## 5分钟快速验证

### 1. 启动服务
```bash
# 克隆项目后，启动所有服务
docker compose up -d

# 等待服务启动完成（约30秒）
docker compose ps
```

### 2. 验证 Manticore 运行状态
```bash
# 检查 Manticore 容器状态
docker compose ps manticore

# 应该显示 "Up" 状态，如果显示 "Restarting" 请查看故障排除部分
```

### 3. 运行测试验证功能
```bash
# 进入后端目录
cd backend

# 激活虚拟环境
source .venv/bin/activate

# 运行连接测试（快速验证）
python app/test_manticore.py --connection-only

# 运行完整功能测试
python app/test_manticore.py
```

### 4. 预期结果
如果一切正常，你应该看到：
```
✅ Health check passed
✅ SQL execution passed  
✅ Table creation passed
✅ Document insertion passed
✅ Search passed - found 1 results
🎉 All tests passed!
```

## 常见启动问题

### 问题1: Manticore 容器一直重启
```bash
# 查看错误日志
docker compose logs manticore

# 常见原因和解决方案：
# 1. 配置文件语法错误 -> 检查 manticore/manticore.conf
# 2. 端口冲突 -> 检查 9306, 9308, 9312 端口是否被占用
# 3. 权限问题 -> 重建数据卷: docker volume rm master-know_manticore-data
```

### 问题2: 测试连接失败
```bash
# 检查端口是否正确映射
docker compose ps manticore

# 手动测试连接
curl -s http://localhost:9308/

# 如果返回 JSON 响应则连接正常
```

### 问题3: 虚拟环境问题
```bash
# 确保在正确的目录
cd backend

# 检查虚拟环境是否存在
ls -la .venv/

# 如果不存在，创建虚拟环境
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## 下一步

验证成功后，你可以：

1. **查看完整文档**: `docs/manticore/README.md`
2. **集成到你的代码**: 参考 `backend/app/services/search/` 中的示例
3. **测试 API 端点**: 访问 `http://localhost:8000/docs` 查看 Swagger 文档
4. **自定义配置**: 修改 `manticore/manticore.conf` 配置文件

## 快速 API 测试

启动服务后，可以通过以下方式测试搜索 API：

```bash
# 健康检查
curl http://localhost:8000/api/v1/search/health

# 如果集成了搜索路由，可以测试搜索功能
curl -X POST http://localhost:8000/api/v1/search/documents \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "limit": 10}'
```

---

遇到问题？查看 [完整文档](README.md) 或检查 [故障排除部分](README.md#故障排除)。
