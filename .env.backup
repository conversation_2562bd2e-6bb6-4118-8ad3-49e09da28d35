# Project Configuration
PROJECT_NAME=Master-Know
STACK_NAME=master-know
ENVIRONMENT=local
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# Docker Images
DOCKER_IMAGE_BACKEND=master-know-backend
DOCKER_IMAGE_FRONTEND=master-know-frontend
TAG=latest

# Security
SECRET_KEY=dev-secret-key-change-in-production
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changethis

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:5173","http://localhost:8000","http://localhost"]

# Database
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=app
POSTGRES_USER=postgres
POSTGRES_PASSWORD=changethis

# OpenAI Configuration
OPENAI_API_KEY=sk-ePLBAF0SMDPSrqZ9VS1RbYSZaphIOrHhFQHnlfjlWjNt8k4Z
OPENAI_BASE_URL=https://ai98.vip/v1
OPENAI_MODEL=gpt-5-nano

# LLM Configuration  
LLM_MAX_TOKENS=4096
LLM_TEMPERATURE=0.2

# Service Configuration
SERVICE_HOST=0.0.0.0
SERVICE_PORT=8000

# Optional: Manticore Search (for future integration)
MANTICORE_HOST=localhost
MANTICORE_PORT=9308