# 知深学习导师 (Master-Know)

**一个个人化 AI 学习伴行系统，旨在通过可追溯、可复盘的引导式对话，帮助用户真正内化知识。**

[![Test](https://github.com/fastapi/full-stack-fastapi-template/workflows/Test/badge.svg)](https://github.com/fastapi/full-stack-fastapi-template/actions?query=workflow%3ATest)
[![Coverage](https://coverage-badge.samuelcolvin.workers.dev/fastapi/full-stack-fastapi-template.svg)](https://coverage-badge.samuelcolvin.workers.dev/redirect/fastapi/full-stack-fastapi-template)

## 核心理念

我们相信，真正的学习不是一次性的“问答”，而是一个持续的、可回溯的探索过程。本项目致力于解决 AI 对话“聊后即忘”的痛点，将碎片化的学习沉淀为结构化的个人知识库。

其核心是验证一个假设：**一个具备长期主题记忆、且能通过交互式摘要轻松回顾的 AI 对话体验，是否能显著提升用户的学习效率和深度。**

## 快速开始

本项目使用 Docker Compose 进行环境管理，让您能一键启动所有服务。

1.  **配置环境**:
    复制 `.env.example` 为 `.env`，并根据需要修改其中的配置。至少，您需要生成新的 `SECRET_KEY`。

2.  **启动服务**:
    ```bash
    docker compose up -d
    ```

3.  **访问应用**:
    - **前端**: `http://localhost:5173`
    - **后端 API 文档**: `http://localhost:8000/docs`

## 文档中心

我们为您准备了详尽的文档，帮助您深入了解项目的方方面面。**所有文档的入口都在这里：**

**[👉 点击这里，进入项目文档中心](./docs/README.md)**

在文档中心，您可以找到：
- **产品定义**: 项目的目标、用户故事和功能需求。
- **架构设计**: 系统的高层视图、数据模型和技术选型。
- **工程指南**: 详细的开发、部署和测试流程。

## 核心模块

### 🔍 Manticore Search 集成
全文搜索和向量搜索引擎，支持语义搜索和混合搜索功能。

- **[📖 完整文档](docs/manticore/README.md)** - 详细的配置、API 和使用指南
- **[⚡ 快速开始](docs/manticore/QUICKSTART.md)** - 5分钟验证和测试指南
- **状态**: ✅ 生产就绪，所有功能已通过测试验证

## 技术栈

- **后端**: FastAPI, SQLModel, PostgreSQL
- **前端**: React, TypeScript, Chakra UI
- **核心引擎**: Manticore Search (用于全文+向量混合搜索)
- **异步任务**: Dramatiq
- **部署**: Docker, Traefik

## 参与贡献

我们欢迎任何形式的贡献！请参考我们的工程指南开始。

## 许可证

本项目基于 MIT 许可证。
