"""
Text-Splitter Engine 测试
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy
from engines.text_splitter.models import Document

class TestTextSplitterEngine:
    
    def setup_method(self):
        """测试前设置"""
        self.engine = TextSplitterEngine()
        self.sample_text = "这是一个测试文档。" * 100  # 创建较长的测试文本
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        assert self.engine is not None
        stats = self.engine.get_stats()
        assert stats["splitters_loaded"] > 0
    
    def test_token_based_splitting(self):
        """测试基于 Token 的分割"""
        strategy = TokenBasedStrategy(max_tokens=50)
        chunks = self.engine.split_text(self.sample_text, strategy)
        
        assert len(chunks) > 1
        assert all(chunk.token_count <= 60 for chunk in chunks)  # 允许一些误差
        assert all(chunk.content for chunk in chunks)  # 确保内容不为空
    
    def test_character_based_splitting(self):
        """测试基于字符的分割"""
        strategy = CharacterBasedStrategy(max_chars=100)
        chunks = self.engine.split_text(self.sample_text, strategy)
        
        assert len(chunks) > 1
        assert all(len(chunk.content) <= 120 for chunk in chunks)  # 允许一些误差
    
    def test_document_splitting(self):
        """测试文档分割"""
        document = Document(
            title="测试文档",
            content=self.sample_text,
            file_type="txt",
            size=len(self.sample_text.encode('utf-8'))
        )
        
        result = self.engine.split_document(document)
        
        assert result.document_id == document.id
        assert result.total_chunks > 0
        assert len(result.chunks) == result.total_chunks
    
    def test_batch_splitting(self):
        """测试批量分割"""
        documents = [
            Document(title=f"文档{i}", content=self.sample_text, file_type="txt", size=len(self.sample_text.encode('utf-8')))
            for i in range(3)
        ]
        
        results = self.engine.batch_split(documents)
        
        assert len(results) == 3
        assert all(result.total_chunks > 0 for result in results)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
